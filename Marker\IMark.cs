﻿using McLaser.EditViewerSk.Entitys;
using System.Collections.Concurrent;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Marker
{
    public delegate void MarkerEventHandler(IMarker sender, IMarkerArg markerArg);


    public enum MarkTargets
    {
        All,
        Selected,
        SelectedButBoundRect,
        Custom,
    }


    //激光加工接口
    public interface IMark
    {
        bool Jump(Vector2 pt);
        bool MarkLine(EntityLine line);
        //bool MarkArc(EntityArc line);
        bool MarkCircle(EntityCircle circle);
        bool MarkPoint(EntityPoint point);
        bool MarkSpline(EntitySpline polygon);
        bool MarkPolyline(EntityLwPolyline polyline);
        bool MarkRectangle(EntityRectangle rectangle);
        //bool MarkDocument(EntityDocument document);
        bool MarkLayer(EntityLayer layer);
    }


}
