﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Core;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Marker;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Net;
using System.Numerics;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;

namespace McLaser.EditViewerSk.Entitys
{

    public enum CircleDirection
    {
        CW,
        CCW
    }

    public class EntityArc : EntityBase
    {

        private Vector2 center;
        private double startAngle;
        private double sweepAngle;
        private double radius;
        private Vector2 startPoint;
        private Vector2 endPoint;


        [JsonIgnore]
        private EntityLwPolyline lwPolyline = new EntityLwPolyline();


        [Category("基础"), DisplayName("半径"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double Radius
        {
            get => this.radius;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                this.radius = value;
                IsNeedToRegen = true;
            }
        }

        [Category("基础"), DisplayName("圆心"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public Vector2 Center
        {
            get => this.center;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                this.center = value;
                IsNeedToRegen = true;
            }
        }

        [Category("基础"), DisplayName("起始角度"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double StartAngle
        {
            get => this.startAngle;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                this.startAngle = value;
                IsNeedToRegen = true;
            }
        }

        [Category("基础"), DisplayName("扫描角度"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double SweepAngle
        {
            get => this.sweepAngle;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                this.sweepAngle = value;
                IsNeedToRegen = true;
            }
        }

        [Category("基础"), DisplayName("起始点"), Browsable(false)]
        public Vector2 StartPoint
        {
            get
            {
                return startPoint;
            }
            set { startPoint = value; }
        }


        [Category("基础"), DisplayName("结束点"), Browsable(false)]
        public Vector2 EndPoint
        {
            get
            {
                return endPoint;
            }
            set { endPoint = value; }
        }

        [Category("基础"), DisplayName("是否顺时针")]
        public bool IsCloseWise
        {
            get
            {
                var vector1 = new Vector2(StartPoint.X - Center.X, StartPoint.Y - Center.Y);
                var vector2 = new Vector2(EndPoint.X - Center.X, EndPoint.Y - Center.Y);
                double crossProduct = vector1.X * vector2.Y - vector1.Y * vector2.X;
                if (crossProduct > 0)
                {
                    return true;
                }
                return false;
            }
        }


        [Category("基础"), DisplayName("长度"), JsonIgnore]
        public  double Length
        {
            get
            {
                return Math.Abs(sweepAngle) * Math.PI / 180.0f * radius;
            }
        }


        public EntityArc()
        {
            Name = "Arc";
           // Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/219.png"));

        }

        public EntityArc(double x, double y, double radius, double startAngle, double sweepAngle)
          : this()
        {
            this.center = new Vector2(x, y);
            this.radius = radius;
            this.startAngle = startAngle;
            this.sweepAngle = sweepAngle;
           // Icon =  new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/219.png"));

        }

        
        //{
        //    get
        //    {
        //        double left = this.StartPoint.X < this.EndPoint.X ? this.StartPoint.X : this.EndPoint.X;
        //        double right = this.StartPoint.X < this.EndPoint.X ? this.EndPoint.X : this.StartPoint.X;
        //        double top = this.StartPoint.Y > this.EndPoint.Y ? this.StartPoint.Y : this.EndPoint.Y;
        //        double bottom = this.StartPoint.Y > this.EndPoint.Y ? this.EndPoint.Y : this.StartPoint.Y;
        //        this._boundingBox = new BoundingBox(left, top, right, bottom);
        //        return this._boundingBox;
        //    }

        //}




        public new  float Angle { get; set; }

        public List<Vector2> Vertices = new List<Vector2>();




        public override void Render(IView view)
        {
            if (view == null || !this.IsRenderable) return;
            if (this.IsNeedToRegen) this.Regen();

            Pen.Color = IsSelected ? SKColors.Red : SKColors.Black;

            (view as ViewBase).DrawArc(Center, Radius, StartAngle, StartAngle + SweepAngle, Pen);
            if (IsSelected)   (view as ViewBase).DrawArcWithArrow(this, 10);

        }

        private void RegenBoundRect()
        {
            // Start by calculating the bounding box of the start and end points
            double minX = Math.Min(StartPoint.X, EndPoint.X);
            double minY = Math.Min(StartPoint.Y, EndPoint.Y);
            double maxX = Math.Max(StartPoint.X, EndPoint.X);
            double maxY = Math.Max(StartPoint.Y, EndPoint.Y);

            // Check if the arc crosses the cardinal axes (0, 90, 180, 270 degrees)
            double start = MathHelper.NormalizeAngle(this.StartAngle);
            double end = MathHelper.NormalizeAngle(start + this.SweepAngle);

            // Normalize angles to be within the same 360-degree cycle
            if (end < start) end += 360;

            // Check for 0 degrees (East)
            if (start <= 0 && end >= 0 || start <= 360 && end >= 360) maxX = Center.X + Radius;
            // Check for 90 degrees (North)
            if (start <= 90 && end >= 90) maxY = Center.Y + Radius;
            // Check for 180 degrees (West)
            if (start <= 180 && end >= 180) minX = Center.X - Radius;
            // Check for 270 degrees (South)
            if (start <= 270 && end >= 270) minY = Center.Y - Radius;

            base.BoundingBox.Set(minX, maxY, maxX, minY);
        }

        public override void Regen()
        {
            //this.RegenVertextList();
            this.RegenBoundRect();
            this.IsNeedToRegen = false;
        }

        private void RegenVertextList()
        {
            this.Children.Clear();
            if (Vertices.Count < 2) return;

           
            lwPolyline.Vertexes.Clear();

            double num1 = Math.Cos((double)this.StartAngle * (Math.PI / 180.0)) * (double)this.Radius + (double)this.Center.X;
            double num2 = Math.Sin((double)this.StartAngle * (Math.PI / 180.0)) * (double)this.Radius + (double)this.Center.Y;
            if ((double)this.SweepAngle > 0.0)
            {
                for (double startAngle = (double)this.StartAngle; startAngle < (double)this.StartAngle + (double)this.SweepAngle - Config.AngleFactor; startAngle += (double)Config.AngleFactor)
                {
                    lwPolyline.Vertexes.Add(new Vector2(Math.Cos(startAngle * (Math.PI / 180.0)) * this.Radius + this.Center.X, Math.Sin(startAngle * (Math.PI / 180.0)) * this.Radius + this.Center.Y));
                }
            }
            else
            {
                for (double startAngle = (double)this.StartAngle; startAngle > (double)this.StartAngle + (double)this.SweepAngle - Config.AngleFactor; startAngle -= (double)Config.AngleFactor)
                {
                    lwPolyline.Vertexes.Add(new Vector2(Math.Cos(startAngle * (Math.PI / 180.0)) * this.Radius + this.Center.X, Math.Sin(startAngle * (Math.PI / 180.0)) * this.Radius + this.Center.Y));
                }
            }
            this.lwPolyline.Vertexes.Add(new Vector2(Math.Cos(((double)this.StartAngle + (double)this.SweepAngle) * (Math.PI / 180.0)) * this.Radius + this.Center.X, Math.Sin(((double)this.StartAngle + (double)this.SweepAngle) * (Math.PI / 180.0)) * this.Radius + this.Center.Y));
            this.lwPolyline.Parent = this;
            this.lwPolyline.Regen();
        }

      


        public override void Translate(Vector2 delta)
        {
            //if (this.IsLocked || delta == Vector2.Zero)
            //    return;
            //this.StartPoint = Vector2.Add(this.StartPoint, delta);
            //this.EndPoint = Vector2.Add(this.EndPoint, delta);
            //this.BoundingBox.Transit(delta);
        }

        public override void Rotate(double angle)
        {
            //if (this.IsLocked || MathHelper.IsZero(angle))
            //    return;
            //this.StartPoint = Vector2.Transform(StartPoint, Matrix3.CreateRotation(angle * ((float)Math.PI / 180f), this.BoundingBox.Center));
            //this.EndPoint = Vector2.Transform(this.EndPoint, Matrix3.CreateRotation(angle * ((float)Math.PI / 180f), this.BoundingBox.Center));
            //this.angle += angle;
            //this.angle = MathHelper.NormalizeAngle(this.angle);
            //this.Regen();
        }

        public override void Rotate(double angle, Vector2 rotateCenter)
        {
            //if (this.IsLocked || MathHelper.IsZero(angle))
            //    return;
            //this.StartPoint = Vector2.Transform(this.StartPoint, Matrix3.CreateRotation(angle * ((float)Math.PI / 180f), rotateCenter));
            //this.EndPoint = Vector2.Transform(this.EndPoint, Matrix3.CreateRotation(angle * ((float)Math.PI / 180f), rotateCenter));
            //this.angle += angle;
            //this.angle = MathHelper.NormalizeAngle(this.angle);
            //this.Regen();
        }

        public override void Scale(Vector2 scale)
        {
            //if (this.IsLocked || scale == Vector2.Zero || scale == Vector2.One)
            //    return;
            //Vector2 vector2 = (this.StartPoint + this.EndPoint) * 0.5f;
            //this.StartPoint = (this.StartPoint - vector2) * scale + vector2;
            //this.EndPoint = (this.EndPoint - vector2) * scale + vector2;
            //this.Regen();
        }

        public override void Scale(Vector2 scale, Vector2 scaleCenter)
        {
            //if (this.IsLocked || scale == Vector2.Zero || scale == Vector2.One)
            //    return;
            //this.StartPoint = (this.StartPoint - scaleCenter) * scale + scaleCenter;
            //this.EndPoint = (this.EndPoint - scaleCenter) * scale + scaleCenter;
            //this.Regen();
        }



        public override bool HitTest(double left, double top, double right, double bottom, double threshold)
        {
            return this.HitTest(new BoundingBox(left, top, right, bottom), threshold);
            //return MathHelper.IntersectArcInRect(new BoundingBox(left, top, right, bottom), Center, Radius, StartAngle, SweepAngle);
        }

        public override bool HitTest(double x, double y, double threshold)
        {
            if (!this.BoundingBox.HitTest(x, y, threshold))
                return false;
            int num = -1;
            if (this.lwPolyline.HitTest(x, y, threshold))
                num = 0;
            return num >= 0;
        }


        public override bool HitTest(BoundingBox br, double threshold)
        {
            if (!this.BoundingBox.HitTest(br, threshold))
                return false;
            int num = -1;
            //if (this.lwPolyline.HitTest(br, threshold))
            num = 0;
            return num >= 0;
        }


        public override bool Mark(IMarkerArg markerArg, IMarkCommand cmd)
        {
            if (!this.IsMarkerable)
                return true;
            bool flag = true;

            if (!this.IsReversable)
            {


                flag &= cmd.MarkArc(markerArg, this, out string buffer);
                if (!flag)
                    return false;
            }

            return flag;
        }

        public override object Clone()
        {

            return new EntityArc()
            {
                Name = this.Name,
                Parent = this.Parent,
                IsSelected = this.IsSelected,
                IsRenderable = this.IsRenderable,
                IsLocked = this.IsLocked,
                StartAngle = this.StartAngle,
                SweepAngle = this.SweepAngle,
                Center = this.Center,
                Radius = this.Radius,
                Index = this.Index,
                IsNeedToRegen = true

            };

        }


    }
}
