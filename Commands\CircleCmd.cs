﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;


namespace McLaser.EditViewerSk.Commands
{
    public class CircleCmd : DrawCmd
    {
        private EntityCircle _circle = null;
        private DocumentBase doc;

        public CircleCmd(DocumentBase doc)
        {
            this.doc = doc;
        }

        protected override IEnumerable<EntityBase> newEntities
        {
            get { return new EntityCircle[1] { _circle }; }
        }

        
        private Step _step = Step.Step1_SpecifyCenter;
        private enum Step
        {
            Step1_SpecifyCenter = 1,
            Step2_SpecityRadius = 2,
        }

        public override void Initialize()
        {
            base.Initialize();

            _step = Step.Step1_SpecifyCenter;
            this.pointer.Mode = IndicatorMode.Locate;
        }

        protected override void Commit()
        {
            if (this.newEntities.Count() == 1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            switch (_step)
            {
                case Step.Step1_SpecifyCenter:
                    if (e.Button == MouseButtons.Left)
                    {
                        _circle = new EntityCircle();
                        _circle.Center = this.pointer.CurrentSnapPoint;
                        _circle.Radius = 0;
                        //_circle.layerId = this.Document.currentLayerId;
                        //_circle.color = this.Document.currentColor;
                        _step = Step.Step2_SpecityRadius;
                    }
                    break;
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            if (_step == Step.Step2_SpecityRadius)
            {
                Vector2 vector = this.pointer.CurrentSnapPoint;
                _circle.Radius = (_circle.Center - this.pointer.CurrentSnapPoint).Length;
                _mgr.FinishCurrentCommand();
            }
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (_step == Step.Step2_SpecityRadius)
            {
                Vector2 vector = this.pointer.CurrentSnapPoint;
                _circle.Radius = (_circle.Center - this.pointer.CurrentSnapPoint).Length;
            }

            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {

            if (_circle != null)
            {
                ViewBase viewer = _mgr.Viewer as ViewBase;
                _circle.Render(viewer);
            }
        }
    }
}
