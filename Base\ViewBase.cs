﻿using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Input;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Core;
using SkiaSharp;
using SkiaSharp.Views.Desktop;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms;
using ICommand = McLaser.EditViewerSk.Interfaces.ICommand;
using Point = System.Drawing.Point;



namespace McLaser.EditViewerSk.Base
{

    public class ViewBase : IView
    {

        //图档
        public DocumentBase Document { get; set; }
        public SKPath SkPath { get; set; }

        //SkiaSharp 控件
        internal SKGLControl Viewer;

        //SkiaSharp 绘图画板
        internal SKCanvas Canvas;

        //默认的绘图参数Pen 
        internal SKPaint DefaultPen = new SKPaint()
        {
            Color = SKColors.LightGray,
            StrokeWidth = 1f,
            Style = SKPaintStyle.Stroke,
            IsAntialias = true
        };


        //命令管理器
        public CommandsMgr _cmdsMgr = null;

        //指针管理器
        public MgrIndicator _pointer = null;

        //动态输入管理器
        public DynamicInputer _dynamicInputer = null;

        public List<EntityBase> Selections => Document.SelectedEntitys;

        //选中Entity
        public EntityBase HittedEntity { get; set; }

        //Undo
        public bool CanUndo => _cmdsMgr.CanUndo;

        //Redo
        public bool CanRedo => _cmdsMgr.CanRedo;

        //宽
        public double Width => Viewer.Width;

        //高
        public double Height => Viewer.Height;

        public List<ObjectSnapPoint> GridSnaps = new List<ObjectSnapPoint>();

        //渲染时间
        public virtual long RenderTime { get; protected set; }

        //渲染时间计数器
        private Stopwatch _stopwatch = new Stopwatch();

        //选中框
        private BoundingBox SelectedBoundRect { get; set; } = new BoundingBox();
        private Vector2 _screenPan = new Vector2();
        private Vector2 _screenDrag = new Vector2();

        private Vector2 _mouseDownPoint = new Vector2();
        private CommandsFactory _cmdsFactory = new CommandsFactory();

        private Point mouseCurrentLocation;
        private Point mouseLeftDownLocation;

        private bool drawPanning = false;
        private SKMatrix _viewMatrix = SKMatrix.Identity;
        private float _worldScale = 1.0f;
        private SKPoint _worldCenter = SKPoint.Empty;
        private SKMatrix _yFlipMatrix = SKMatrix.CreateScale(1, -1);

        public EventHandler<MouseEventArgs> MouseMove;

        double _x = 0, _y = 0;

        private IRenderer _renderer;

        //构造函数
        public ViewBase(SKGLControl element, DocumentBase doc)
        {
            Viewer = element;
            Document = doc;
            SelectedBoundRect = BoundingBox.Empty;

            // Define the total bounds of the world for the quadtree - 使用更大的默认边界
            var worldBounds = new SKRect(-100000, -100000, 100000, 100000);
            _renderer = new CachedSkiaRenderer(worldBounds);

            _cmdsMgr = new CommandsMgr(this);
            _cmdsMgr.commandFinished += this.OnCommandFinished;
            _cmdsMgr.commandCanceled += this.OnCommandCanceled;
            _dynamicInputer = new DynamicInputer(this);
            _dynamicInputer.cmdInput.finish += this.OnCmdInputResurn;
            _dynamicInputer.cmdInput.cancel += this.OnCmdInputResurn;
            _pointer = new MgrIndicator(this);
            SkPath = new SKPath() { FillType = SKPathFillType.EvenOdd };

            Initialize(Viewer);
            InitGridSnaps();
        }

        public ViewBase(SKGLControl element)
        {
            Viewer = element;
            SelectedBoundRect = BoundingBox.Empty;
            _cmdsMgr = new CommandsMgr(this);
            _cmdsMgr.commandFinished += this.OnCommandFinished;
            _cmdsMgr.commandCanceled += this.OnCommandCanceled;
            _dynamicInputer = new DynamicInputer(this);
            _dynamicInputer.cmdInput.finish += this.OnCmdInputResurn;
            _dynamicInputer.cmdInput.cancel += this.OnCmdInputResurn;
            _pointer = new MgrIndicator(this);
            Initialize(Viewer);
            InitGridSnaps();
        }


        //初始化
        private void Initialize(SKGLControl element)
        {
            element.PaintSurface += SkContainer_PaintSurface;
            element.MouseDown += SkContainer_MouseDown;
            element.MouseUp += SkContainer_MouseUp;
            element.MouseMove += SkContainer_MouseMove;
            element.MouseWheel += SkContainer_MouseWheel;
            element.KeyDown += SkContainer_KeyDown;
            element.KeyUp += SkContainer_KeyUp;
            element.MouseEnter += SkContainer_MouseEnter;
            element.MouseLeave += SkContainer_MouseLeave;
            element.SizeChanged += SkContainer_SizeChanged;
        }

        public void OnMouseMove(object sender, MouseEventArgs e)
        {
            MouseMove?.Invoke(sender as IInputElement, e);
        }


        //绘制网格
        public void InitGridSnaps()
        {
            for (int i = -500; i <= 500; i += 10)
            {
                for (int j = -500; j <= 500; j += 10)
                {
                    GridSnaps.Add(new ObjectSnapPoint(ObjectSnapMode.Grid, new Vector2(i, j)));
                }
            }
        }

        //窗口尺寸变化时
        private void SkContainer_SizeChanged(object sender, EventArgs e)
        {

        }

        Point _ptStart;
        //鼠标按下
        private void SkContainer_MouseDown(object sender, MouseEventArgs e)
        {
            var cur = e.Location;// e.GetPosition(sender as IInputElement);
            var pt1 = CanvasToModel(new Vector2((float)cur.X, (float)cur.Y));
            var pt2 = ModelToCanvas(pt1);



            Command cmd = _pointer.OnMouseDown(sender as IInputElement, e);
            _mouseDownPoint.X = (float)cur.X;
            _mouseDownPoint.Y = (float)cur.Y;
            _ptStart = new Point(cur.X, cur.Y);
            if (_cmdsMgr.CurrentCmd != null)
            {
                _cmdsMgr.OnMouseDown(e);
                RepaintCanvas(true);
            }
            else
            {
                if (cmd != null)
                {
                    _cmdsMgr.DoCommand(cmd);
                    RepaintCanvas();
                }
            }


            if (e.Button == MouseButtons.Left)
            {

                this.mouseLeftDownLocation = cur;
                this.mouseCurrentLocation = cur;
                this.drawPanning = true;
                //RepaintCanvas();

            }
            else if (e.Button == MouseButtons.Middle)
            {
                this.mouseCurrentLocation = cur;
            }

            this.mouseCurrentLocation = cur;
        }

        //鼠标弹起
        private void SkContainer_MouseUp(object sender, MouseEventArgs e)
        {
            var cur = e.Location;// GetPosition(sender as IInputElement);
            _pointer.OnMouseUp(sender as IInputElement, e);
            if (e.Button == MouseButtons.Left)// e.LeftButton.HasFlag(MouseButtonState.Pressed))
            {

            }
            else if (e.Button == MouseButtons.Middle)
            {
                this.mouseCurrentLocation = cur;
                _screenPan += _screenDrag;
                _screenDrag.X = 0;
                _screenDrag.Y = 0;
                return;
            }

            if (_cmdsMgr.CurrentCmd != null)
            {
                _cmdsMgr.OnMouseUp(e);
            }
        }

        //鼠标移动
        private void SkContainer_MouseMove(object sender, MouseEventArgs e)
        {
            var cur = e.Location;
            OnMouseMove(sender, e);
            _pointer.OnMouseMove(sender, e);
            _dynamicInputer.OnMouseMove(sender, e);

            if (e.Button == MouseButtons.Middle)
            {
                float dx = cur.X - mouseCurrentLocation.X;
                float dy = cur.Y - mouseCurrentLocation.Y;

                var translation = SKMatrix.CreateTranslation(dx, dy);
                _viewMatrix = SKMatrix.Concat(translation, _viewMatrix); // Correct order

                mouseCurrentLocation = cur; // This was the missing piece
                Viewer.Invalidate();
            }

            if (_cmdsMgr.CurrentCmd != null)
            {
                _cmdsMgr.OnMouseMove(e);
            }
        }

        //鼠标滚轮滚动
        private void SkContainer_MouseWheel(object sender, MouseEventArgs e)
        {
            var zoomFactor = e.Delta > 0 ? 1.2f : 1 / 1.2f; // A bit more noticeable zoom
            var mousePosition = e.Location;

            // To zoom at the mouse position, we need to transform the pivot point
            // into the coordinate system before the zoom.
            if (_viewMatrix.TryInvert(out var invertedView))
            {
                var pivot = invertedView.MapPoint(mousePosition.X, mousePosition.Y);

                var scaleMatrix = SKMatrix.CreateScale(zoomFactor, zoomFactor, pivot.X, pivot.Y);
                _viewMatrix = SKMatrix.Concat(_viewMatrix, scaleMatrix);

                Viewer.Invalidate();
            }
        }

        //鼠标进入
        private void SkContainer_MouseEnter(object sender, EventArgs e)
        {
            Viewer.Focus();
        }

        //鼠标离开
        private void SkContainer_MouseLeave(object sender, EventArgs e)
        {
        }

        //按键抬起
        public void SkContainer_KeyUp(object sender, KeyEventArgs e)
        {
            _pointer.OnKeyUp(e);
            if (_cmdsMgr != null)
            {
                _cmdsMgr.OnKeyUp(e);
            }

        }

        //按键按下
        public void SkContainer_KeyDown(object sender, KeyEventArgs e)
        {
            _pointer.OnKeyDown(e);
            if (_cmdsMgr.CurrentCmd != null)
            {
                _cmdsMgr.OnKeyDown(e);
            }
            else
            {
                if (_dynamicInputer.StartCmd(e))
                {
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    Document.SelectedEntitys.Clear();
                }
                else if (e.KeyCode == Keys.Delete)
                {
                    if (Document.SelectedEntitys.Count > 0)
                    {
                        //DeleteCmd cmd = new DeleteCmd();
                        //this.OnCommand(cmd);
                    }
                }
            }

        }


        // 渲染缓存
        private SKImage _staticLayerCache;
        private bool _isStaticCacheDirty = true;
        private readonly static object _lock = new object();

        private void SkContainer_PaintSurface(object sender, SKPaintGLSurfaceEventArgs e)
        {
            _stopwatch.Restart();
            Canvas = e.Surface.Canvas;
            var info = e.Info;

            Canvas.Clear(SKColors.WhiteSmoke);

            // 如果缓存需要更新，则重建它
            if (_staticLayerCache == null || _isStaticCacheDirty)
            {
                UpdateStaticCache(info);
                _isStaticCacheDirty = false;
            }

            // 直接绘制缓存，它内部已经包含了所有变换
            if (_staticLayerCache != null)
            {
                System.Diagnostics.Debug.WriteLine($"绘制静态缓存到主Canvas，缓存大小: {_staticLayerCache.Width}x{_staticLayerCache.Height}");
                Canvas.DrawImage(_staticLayerCache, 0, 0);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("警告: 静态缓存为空");
            }

            // 测试矩形已移除，问题已解决

            // 绘制动态UI覆盖物
            DrawDynamicOverlays(Canvas);

            _stopwatch.Stop();
            RenderTime = _stopwatch.ElapsedMilliseconds;
        }

        private void UpdateStaticCache(SKImageInfo info)
        {
            _staticLayerCache?.Dispose();

            System.Diagnostics.Debug.WriteLine($"更新静态缓存，画布大小: {info.Width}x{info.Height}");

            // Use a temporary bitmap for the cache
            using (var surface = SKSurface.Create(info))
            {
                if (surface == null)
                {
                    System.Diagnostics.Debug.WriteLine("错误: 无法创建Surface");
                    return;
                }
                var canvas = surface.Canvas;
                canvas.Clear(SKColors.WhiteSmoke);

                // Apply the view matrix to the cache canvas
                canvas.SetMatrix(in _viewMatrix);

                // 调试Canvas状态
                System.Diagnostics.Debug.WriteLine($"Canvas变换矩阵: {canvas.TotalMatrix}");
                System.Diagnostics.Debug.WriteLine($"Canvas裁剪边界: {canvas.LocalClipBounds}");

                // Draw static background elements
                DrawGrids();
                DrawAxes();

                // Draw all the entities using the renderer
                // The renderer will use the canvas's current matrix
                var visibleWorldRect = GetVisibleWorldRect();
                System.Diagnostics.Debug.WriteLine($"渲染可见区域: {visibleWorldRect}");
                _renderer.Render(canvas, visibleWorldRect);

                // Create an immutable snapshot
                _staticLayerCache = surface.Snapshot();
                System.Diagnostics.Debug.WriteLine($"静态缓存创建完成，大小: {_staticLayerCache.Width}x{_staticLayerCache.Height}");
            }
        }

        private void DrawDynamicOverlays(SKCanvas canvas)
        {
            // Save the current state of the canvas (which is in screen coordinates)
            canvas.Save();

            // Apply the world transformation for drawing model-space overlays
            canvas.SetMatrix(in _viewMatrix);

            // Draw elements that live in the model space
            _cmdsMgr.OnPaint(this); // e.g., rubber-band line for new geometry
            DrawSelectedBBox();     // Bounding box of selected items

            // Restore the canvas to screen coordinates
            canvas.Restore();

            // Draw elements that live in the screen space (UI overlays)
            DrawIndicator(); // e.g., mouse cursor crosshairs
            //DrawRuler();
            DrawWatermark(canvas.DeviceClipBounds.Width, canvas.DeviceClipBounds.Height);
            DrawLabel(canvas.DeviceClipBounds.Width, canvas.DeviceClipBounds.Height);
        }

        private SKRect GetVisibleWorldRect()
        {
            if (Viewer == null) return SKRect.Empty;

            // The view rectangle in screen coordinates
            var viewRect = new SKRect(0, 0, Viewer.Width, Viewer.Height);

            // Get the inverse of the current view matrix
            if (_viewMatrix.TryInvert(out var invertedMatrix))
            {
                // Transform the screen rectangle to world coordinates
                return invertedMatrix.MapRect(viewRect);
            }

            // Fallback if matrix is not invertible
            return SKRect.Empty;
        }

        

        private void SetPart(SKRect rect, float margin)
        {
            if (rect.IsEmpty)
            {
                _viewMatrix = SKMatrix.Identity;
                return;
            }

            var contentRect = rect;
            contentRect.Inflate(margin, margin);
            var viewRect = new SKRect(0, 0, (float)Viewer.Width, (float)Viewer.Height);

            // 计算缩放因子，同时包含Y轴翻转
            float scaleX = viewRect.Width / contentRect.Width;
            float scaleY = viewRect.Height / contentRect.Height;
            float scale = Math.Min(scaleX, scaleY);

            // 创建一个复合矩阵，一步到位
            // 1. 将内容中心平移到原点
            // 2. 进行缩放，同时通过-scale翻转Y轴
            // 3. 将变换后的内容平移到视图中心
            var matrix1 = SKMatrix.CreateTranslation(-contentRect.MidX, -contentRect.MidY);
            var matrix2 = SKMatrix.CreateScale(scale, -scale);
            var matrix3 = SKMatrix.CreateTranslation(viewRect.MidX, viewRect.MidY);

            _viewMatrix = SKMatrix.Concat(SKMatrix.Concat(matrix3, matrix2), matrix1);
        }


        public void SetSelectedBoxPart()
        {
            if (SelectedBoundRect.Left == 0 && SelectedBoundRect.Right == 0 && SelectedBoundRect.Top == 0 && SelectedBoundRect.Bottom == 0)
            {
                return;
            }
            SetPart(new SKRect((float)SelectedBoundRect.Left, (float)SelectedBoundRect.Top, (float)SelectedBoundRect.Right, (float)SelectedBoundRect.Bottom), 200);
        }

  

        public void DrawDot(double x, double y)
        {
            _x = x;
            _y = y;
            //RepaintCanvas();
        }

        //按钮双击
        public void OnMouseDoubleClick(MouseEventArgs e)
        {
            _pointer.OnMouseDoubleClick(e);
        }

        //渲染
        private void OnRender()
        {
            // 使用新的渲染器进行绘制
            var visibleWorldRect = new SKRect();
            if (Canvas.GetLocalClipBounds(out var localClip))
            {
                visibleWorldRect = localClip;
            }
            _renderer.Render(this.Canvas, visibleWorldRect);

            //绘制指示器
            _cmdsMgr.OnPaint(this);
        }

        public void AddEntityToRenderer(EntityBase entity)
        {
            _renderer.AddEntity(entity);
            RepaintCanvas();
        }

        public void AddEntitiesToRenderer(IEnumerable<EntityBase> entities)
        {
            foreach (var entity in entities)
            {
                _renderer.AddEntity(entity);
            }
            _isStaticCacheDirty = true;
            // 批量添加时不立即重绘，由调用者控制
        }

        public void AddEntitiesToRendererBatch(IEnumerable<EntityBase> entities, bool forceRepaint = true)
        {
            var entityList = entities.ToList();
            int totalCount = entityList.Count;
            int processedCount = 0;
            int addedCount = 0;

            System.Diagnostics.Debug.WriteLine($"开始批量添加 {totalCount} 个图元到渲染器");

            foreach (var entity in entityList)
            {
                if (entity != null && entity.IsRenderable)
                {
                    _renderer.AddEntity(entity);
                    addedCount++;

                    // 调试：检查前几个图元的信息
                    if (addedCount <= 5)
                    {
                        System.Diagnostics.Debug.WriteLine($"图元 {addedCount}: {entity.GetType().Name}, 边界: {entity.BoundsSK}, 可渲染: {entity.IsRenderable}");
                    }
                }

                processedCount++;

                // 每处理10000个图元强制一次垃圾回收，避免内存压力
                if (processedCount % 10000 == 0)
                {
                    GC.Collect(0, GCCollectionMode.Optimized);
                    System.Diagnostics.Debug.WriteLine($"已处理 {processedCount}/{totalCount} 个图元，已添加 {addedCount} 个");
                }
            }

            System.Diagnostics.Debug.WriteLine($"批量添加完成: 总计 {totalCount} 个，成功添加 {addedCount} 个到渲染器");

            _isStaticCacheDirty = true;
            if (forceRepaint)
            {
                RepaintCanvas();
            }
        }

        public void RemoveEntityFromRenderer(EntityBase entity)
        {
            _renderer.RemoveEntity(entity);
            RepaintCanvas();
        }

        public void RemoveEntitiesFromRenderer(IEnumerable<EntityBase> entities)
        {
            foreach (var entity in entities)
            {
                _renderer.RemoveEntity(entity);
            }
            RepaintCanvas();
        }

        public void UpdateEntityInRenderer(EntityBase entity)
        {
            _renderer.UpdateEntity(entity);
            RepaintCanvas();
        }

        public void ClearRenderer()
        {
            _renderer.Clear();
            _isStaticCacheDirty = true;
            RepaintCanvas();
        }

        public void ResetRenderer(SKRect worldBounds)
        {
            _renderer.Clear();

            // 确保世界边界足够大，至少包含内容边界的1.5倍
            var expandedBounds = worldBounds;
            if (!worldBounds.IsEmpty)
            {
                var centerX = worldBounds.MidX;
                var centerY = worldBounds.MidY;
                var expandedWidth = Math.Max(worldBounds.Width * 1.5f, 1000f);
                var expandedHeight = Math.Max(worldBounds.Height * 1.5f, 1000f);

                expandedBounds = new SKRect(
                    centerX - expandedWidth / 2,
                    centerY - expandedHeight / 2,
                    centerX + expandedWidth / 2,
                    centerY + expandedHeight / 2
                );
            }
            else
            {
                // 如果边界为空，使用默认的大边界
                expandedBounds = new SKRect(-50000, -50000, 50000, 50000);
            }

            _renderer = new CachedSkiaRenderer(expandedBounds);
            _isStaticCacheDirty = true;
            RepaintCanvas();
        }

        //绘制网格
        internal void DrawGrids()
        {
            SKColor GridColor = SKColors.LightGray;
            SKColor AxisColor = SKColors.DarkGray;
            using var gridPaint = new SKPaint
            {
                Color = GridColor,
                StrokeWidth = 1,
                IsAntialias = true,
                Style = SKPaintStyle.Stroke
            };

            using var axisPaint = new SKPaint
            {
                Color = AxisColor,
                StrokeWidth = 2,
                IsAntialias = true,
                Style = SKPaintStyle.Stroke
            };

            SKPaint pen = new SKPaint
            {
                Color = SKColors.DarkGray,         // 设置颜色
                StrokeWidth = 0.3f,              // 设置线宽
                Style = SKPaintStyle.Stroke   // 设置为仅绘制边框
            };

            for (int i = -500; i <= 500; i += Config.DocumentGridInterval)
            {
                Vector2 pt1 = new Vector2((float)i, 500f);
                Vector2 pt2 = new Vector2((float)i, -500f);
                DrawLine(pt1, pt2, gridPaint);

            }
            for (int j = -500; j <= 500; j += Config.DocumentGridInterval)
            {
                Vector2 pt1 = new Vector2(-500f, (float)j);
                Vector2 pt2 = new Vector2(500f, (float)j);
                DrawLine(pt1, pt2, gridPaint);
            }
        }

        //绘制坐标轴
        private void DrawAxes()
        {
            SKPaint pen = new SKPaint
            {
                Color = SKColors.DarkGray,         // 设置颜色
                StrokeWidth = 1f,              // 设置线宽
                Style = SKPaintStyle.Stroke   // 设置为仅绘制边框
            };

            Vector2 pt1 = new Vector2(-10000f, (float)0);
            Vector2 pt2 = new Vector2(10000f, (float)0);
            DrawLine(pt1, pt2, pen);

            Vector2 pt3 = new Vector2((float)0, 10000f);
            Vector2 pt4 = new Vector2((float)0, -10000f);
            DrawLine(pt3, pt4, pen);
        }

        //绘制鼠标操作
        private void DrawIndicator()
        {
            _pointer.OnPaint(this);
        }

        //绘制标尺Ruler
        private void DrawRuler()
        {

            float RulerThickness = 30; // 标尺厚度
            SKColor RulerBackgroundColor = SKColors.LightGray; // 标尺背景颜色
            SKColor TickColor = SKColors.Black; // 刻度颜色
            SKColor TextColor = SKColors.Black; // 文本颜色
            float MajorTickLength = 10; // 主刻度线长度
            float MinorTickLength = 5; // 次刻度线长度
            float TextMargin = 3; // 文本与刻度线的间距

            using var backgroundPaint = new SKPaint { Color = RulerBackgroundColor };
            using var tickPaint = new SKPaint { Color = TickColor, StrokeWidth = 1, IsAntialias = true };
            using var textPaint = new SKPaint
            {
                Color = TextColor,
                TextSize = 12,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName("Arial")
            };

            // 绘制标尺背景
            Canvas.DrawRect(0, 0, (float)Width, RulerThickness, backgroundPaint); // 顶部标尺
            Canvas.DrawRect(0, 0, RulerThickness, (float)Height, backgroundPaint); // 左侧标尺

            // 获取逻辑坐标范围
            Vector2 topLeft = CanvasToModel(new Vector2(0, 0));// new SKPoint(0, 0);
            Vector2 bottomRight = CanvasToModel(new Vector2((float)Width, (float)Height));

            // 动态调整刻度间距
            float scale = _viewMatrix.ScaleX;
            float majorTickSpacing = 50; // 主刻度间距
            float minorTickSpacing = majorTickSpacing / 5; // 次刻度间距



            // 绘制水平标尺（顶部）
            for (float x = (float)topLeft.X; x < bottomRight.X; x += majorTickSpacing)
            {
                float screenX = (float)x + RulerThickness; // 转换为屏幕坐标

                Canvas.DrawLine(screenX, RulerThickness - MajorTickLength, screenX, RulerThickness, tickPaint);

                // 绘制主刻度文字
                var text = $"{Math.Round(screenX, 2)}";
                var textBounds = new SKRect();
                textPaint.MeasureText(text, ref textBounds);
                Canvas.DrawText(text, (float)ModelToCanvas(screenX - textBounds.MidX), RulerThickness - MajorTickLength - TextMargin, textPaint);

                // 绘制次刻度
                //for (float minorX = x + minorTickSpacing; minorX < x + majorTickSpacing; minorX += minorTickSpacing)
                //{
                //    float screenMinorX = (float)(minorX )  + RulerThickness;
                //    Canvas.DrawLine(screenMinorX, RulerThickness - MinorTickLength, screenMinorX, RulerThickness, tickPaint);
                //}
            }

            // 绘制垂直标尺（左侧）
            //for (float y = (float)Math.Floor(topLeft.Y / majorTickSpacing) * majorTickSpacing; y < bottomRight.Y; y += majorTickSpacing)
            //{
            //    float screenY = (y - topLeft.Y) * scale + RulerThickness; // 转换为屏幕坐标
            //    Canvas.DrawLine(RulerThickness - MajorTickLength, screenY, RulerThickness, screenY, tickPaint);

            //    // 绘制主刻度文字
            //    var text = $"{Math.Round(y, 2)}";
            //    var textBounds = new SKRect();
            //    textPaint.MeasureText(text, ref textBounds);
            //    Canvas.DrawText(text, RulerThickness - MajorTickLength - textBounds.Width - TextMargin, screenY + textBounds.MidY, textPaint);

            //    // 绘制次刻度
            //    for (float minorY = y + minorTickSpacing; minorY < y + majorTickSpacing; minorY += minorTickSpacing)
            //    {
            //        float screenMinorY = (minorY - topLeft.Y) * scale + RulerThickness;
            //        Canvas.DrawLine(RulerThickness - MinorTickLength, screenMinorY, RulerThickness, screenMinorY, tickPaint);
            //    }
            //}


        }

        //绘制选择框
        private void DrawSelectedBBox()
        {
            this.SelectedBoundRect.Clear();
            if (Document.SelectedEntitys == null) return;

            foreach (EntityBase entity in Document.SelectedEntitys)
            {
                if (entity == null) continue;
                this.SelectedBoundRect.Union(entity.BoundingBox);
            }

            if (this.SelectedBoundRect.IsEmpty)
                return;
            this.SelectedBoundRect.Draw(this);
        }

        private void DrawWatermark(int width, int height)
        {

            string watermarkText = "MCT 铭创智能";
            var paint = new SKPaint
            {
                Color = SKColors.Gray.WithAlpha(20),
                TextSize = 40,
                IsAntialias = true,
                TextAlign = SKTextAlign.Center,
                Typeface = SKTypeface.FromFamilyName("微软雅黑", SKFontStyle.Bold)
            };


            var textBounds = new SKRect();
            paint.MeasureText(watermarkText, ref textBounds);
            float textWidth = textBounds.Width;
            float textHeight = textBounds.Height;

            float angle = -45;
            Canvas?.Save();
            Canvas?.RotateDegrees(angle, width / 2, height / 2);
            float stepX = textWidth * 2;
            float stepY = textHeight * 2;
            for (float y = -height; y < height * 2; y += stepY)
            {
                for (float x = -width; x < width * 2; x += stepX)
                {
                    Canvas?.DrawText(watermarkText, x, y, paint);
                }
            }

            Canvas?.Restore();
        }


        private void DrawLabel(int width, int height)
        {
            // 标签内容
            string labelText = "内测版本";

            // 标签尺寸和位置
            float padding = 15;
            float labelWidth = 120;  // 标签宽度
            float labelHeight = 35; // 标签高度
            float cornerRadius = 12; // 圆角半径
            float labelX = width - labelWidth - padding;
            float labelY = padding;

            // 添加阴影效果
            using (var shadowPaint = new SKPaint())
            {
                shadowPaint.IsAntialias = true;
                shadowPaint.Color = SKColors.Black.WithAlpha(50); // 半透明黑色
                shadowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, 4);

                // 绘制阴影
                var shadowRect = new SKRect(labelX + 2, labelY + 2, labelX + labelWidth + 2, labelY + labelHeight + 2);
                Canvas?.DrawRoundRect(shadowRect, cornerRadius, cornerRadius, shadowPaint);
            }

            // 绘制圆角矩形标签背景
            var rect = new SKRect(labelX, labelY, labelX + labelWidth, labelY + labelHeight);

            using (var paint = new SKPaint())
            {
                paint.IsAntialias = true;
                paint.Style = SKPaintStyle.Fill;

                // 优化的渐变背景
                paint.Shader = SKShader.CreateLinearGradient(
                    new SKPoint(rect.Left, rect.Top),
                    new SKPoint(rect.Right, rect.Bottom),
                    new[] { SKColors.LightGoldenrodYellow, SKColors.Orange },
                    null,
                    SKShaderTileMode.Clamp
                );

                Canvas?.DrawRoundRect(rect, cornerRadius, cornerRadius, paint);
            }

            // 绘制标签边框
            using (var borderPaint = new SKPaint())
            {
                borderPaint.IsAntialias = true;
                borderPaint.Style = SKPaintStyle.Stroke;
                borderPaint.StrokeWidth = 1.5f;
                borderPaint.Color = SKColors.SaddleBrown;

                Canvas?.DrawRoundRect(rect, cornerRadius, cornerRadius, borderPaint);
            }

            // 绘制标签文字
            using (var textPaint = new SKPaint())
            {
                textPaint.IsAntialias = true;
                textPaint.TextSize = 16; // 更精致的字体大小
                textPaint.Color = SKColors.White;
                textPaint.TextAlign = SKTextAlign.Center;
                textPaint.Typeface = SKTypeface.FromFamilyName("Microsoft YaHei", SKFontStyle.Bold);

                // 文字位置居中
                float textX = rect.MidX;
                float textY = rect.MidY + (textPaint.TextSize / 3); // 修正垂直居中偏移
                Canvas?.DrawText(labelText, textX, textY, textPaint);
            }
        }




        public bool HitTest(System.Windows.Point mousePosition, out EntityBase hittedEntity)
        {
            double tolerance = 5.0 / _viewMatrix.ScaleX; // Tolerance in model coordinates
            var modelPoint = CanvasToModel(new Vector2((float)mousePosition.X, (float)mousePosition.Y));
            var queryRect = new SKRect((float)(modelPoint.X - tolerance), (float)(modelPoint.Y - tolerance), (float)(modelPoint.X + tolerance), (float)(modelPoint.Y + tolerance));

            var candidateEntities = ((CachedSkiaRenderer)_renderer).Query(queryRect);

            foreach (var entity in candidateEntities)
            {
                if (entity != null && entity.HitTest(this, mousePosition, out hittedEntity))
                {
                    entity.IsSelected = true;
                    HittedEntity = hittedEntity;
                    return true;
                }
            }

            hittedEntity = null;
            return false;
        }

        public int HitTest(Point mouseDownPosition, Point mouseCurPosition, out List<EntityBase> hittedEntitys)
        {
            var pt1 = CanvasToModel(new Vector2(Math.Min(mouseDownPosition.X, mouseCurPosition.X), Math.Min(mouseDownPosition.Y, mouseCurPosition.Y)));
            var pt2 = CanvasToModel(new Vector2(Math.Max(mouseDownPosition.X, mouseCurPosition.X), Math.Max(mouseDownPosition.Y, mouseCurPosition.Y)));
            var queryRect = new SKRect((float)pt1.X, (float)pt1.Y, (float)pt2.X, (float)pt2.Y);

            hittedEntitys = new List<EntityBase>();
            var candidateEntities = ((CachedSkiaRenderer)_renderer).Query(queryRect);

            var hitTestBox = new BoundingBox(queryRect.Left, queryRect.Top, queryRect.Right, queryRect.Bottom);

            foreach (var entity in candidateEntities)
            {
                if (entity != null && entity.HitTest(hitTestBox, 0.02f))
                {
                    entity.IsSelected = true;
                    hittedEntitys.Add(entity);
                }
                else if (entity != null)
                {
                    entity.IsSelected = false;
                }
            }
            return hittedEntitys.Count;
        }

        public void AppendEntity(EntityBase entity)
        {
            //Block modelSpace = Document.Database.BlockTable["ModelSpace"] as Block;
            //modelSpace.AppendEntity(entity);
        }

        public void RepaintCanvas(bool bufferBitmapToRedraw = false)
        {
            //this.OnRender();
            Viewer.Invalidate();
        }

        public void OnZoomFit(BoundingBox br = null)
        {
            BoundingBox boundsToFit;

            if (br == null)
            {
                // 如果没有提供边界，则从所有图层计算
                boundsToFit = new BoundingBox();
                foreach (EntityLayer layer in this.Document.Layers)
                    boundsToFit.Union(layer.BoundingBox);
            }
            else
            {
                // 使用提供的边界
                boundsToFit = br;
            }

            System.Diagnostics.Debug.WriteLine($"缩放适应边界: {boundsToFit.Left}, {boundsToFit.Top}, {boundsToFit.Right}, {boundsToFit.Bottom}");

            // 如果最终的边界是空的，则显示一个默认视图
            if (boundsToFit.IsEmpty)
            {
                System.Diagnostics.Debug.WriteLine("边界为空，使用默认视图");
                SetPart(new SKRect(-500, 500, 500, -500), 100);
            }
            else
            {
                // 使用正确的边界来设置视图
                var rect = new SKRect((float)boundsToFit.Left, (float)boundsToFit.Top, (float)boundsToFit.Right, (float)boundsToFit.Bottom);
                System.Diagnostics.Debug.WriteLine($"设置视图矩形: {rect}");
                SetPart(rect, 100);
            }

            System.Diagnostics.Debug.WriteLine($"当前视图矩阵: {_viewMatrix}");

            // 标记缓存为脏并重绘
            _isStaticCacheDirty = true;
            RepaintCanvas();
        }

        private void OnCmdInputResurn(DynInputCtrl sender, DynInputResult result)
        {
            switch (result.status)
            {
                case DynInputStatus.OK:
                    {
                        DynInputResult<string> cmdInputRet = result as DynInputResult<string>;
                        Command cmd = _cmdsFactory.NewCommand(cmdInputRet.value.ToLower());
                        if (cmd != null)
                        {
                            this.OnCommand(cmd);
                        }
                    }
                    break;

                case DynInputStatus.Cancel:
                    break;

                case DynInputStatus.Error:
                    break;

                default:
                    break;
            }
        }

        public void OnCommand(ICommand cmd)
        {
            _cmdsMgr.DoCommand(cmd as Command);
        }

        public void OnCommandFinished(Command cmd)
        {
            _isStaticCacheDirty = true;
            this.RepaintCanvas(true);
        }

        public void OnCommandCanceled(Command cmd)
        {
            this.RepaintCanvas(false);
        }

        public void OnSelectionChanged()
        {
            _pointer.OnSelectionChanged();
            this.RepaintCanvas(true);
        }

        public void DrawArrow(Vector2 start, Vector2 end, int size)
        {
            float arrowHeadSize = size / Math.Abs(_viewMatrix.ScaleX);

            if (arrowHeadSize > 10) arrowHeadSize = 10;
            float arrowAngle = 30;

            // 计算方向向量
            var direction = new SKPoint((float)end.X - (float)start.X, (float)end.Y - (float)start.Y);
            var length = (float)Math.Sqrt(direction.X * direction.X + direction.Y * direction.Y);
            direction = new SKPoint(direction.X / length, direction.Y / length);

            // 箭头两侧点
            float angleRad = arrowAngle * (float)Math.PI / 180;
            //arrowHeadSize =Math.Abs( (view as ViewBase).ModelToCanvas(arrowHeadSize));
            var left = new SKPoint(
                (float)end.X - arrowHeadSize * (direction.X * (float)Math.Cos(angleRad) - direction.Y * (float)Math.Sin(angleRad)),
                (float)end.Y - arrowHeadSize * (direction.Y * (float)Math.Cos(angleRad) + direction.X * (float)Math.Sin(angleRad))
            );

            var right = new SKPoint(
                (float)end.X - arrowHeadSize * (direction.X * (float)Math.Cos(-angleRad) - direction.Y * (float)Math.Sin(-angleRad)),
                (float)end.Y - arrowHeadSize * (direction.Y * (float)Math.Cos(-angleRad) + direction.X * (float)Math.Sin(-angleRad))
            );

            var mEnd = ModelToCanvas(end);
            var mLeft = ModelToCanvas(new Vector2(left.X, left.Y));
            var mRight = ModelToCanvas(new Vector2(right.X, right.Y));


            var vertices = new[] { new SKPoint((float)mEnd.X, (float)mEnd.Y), new SKPoint((float)mLeft.X, (float)mLeft.Y), new SKPoint((float)mRight.X, (float)mRight.Y) };

            var colors = new[] { SKColors.Red.WithAlpha(90), SKColors.Red.WithAlpha(90), SKColors.Red.WithAlpha(90) };

            var skVertices = SKVertices.CreateCopy(SKVertexMode.Triangles, vertices, colors);

            var paint = new SKPaint()
            {
                IsAntialias = true,
                Style = SKPaintStyle.Fill,
                //Color = SKColors.Black
            };

            Canvas?.DrawVertices(skVertices, SKBlendMode.SrcOver, paint);

        }

        public void DrawArcWithArrow(EntityArc arc, int size)
        {
            SKPoint center = new SKPoint((float)arc.Center.X, (float)arc.Center.Y);
            float radius = (float)arc.Radius;
            float startAngle = (float)arc.StartAngle;
            float sweepAngle = (float)arc.SweepAngle;

            // 箭头相关参数
            float baseArrowSize = size; // 箭头基础长度
            float arrowHeadSize = baseArrowSize / Math.Abs(_viewMatrix.ScaleX); // 动态调整箭头大小
            float arrowAngle = 30;
            if (arrowHeadSize > 4) arrowHeadSize = 4;
            // 计算结束点角度
            float endAngle = startAngle + sweepAngle;
            float endAngleRad = endAngle * (float)Math.PI / 180;

            // 计算圆弧结束点坐标
            SKPoint endPoint = new SKPoint(
                center.X + radius * (float)Math.Cos(endAngleRad),
                center.Y + radius * (float)Math.Sin(endAngleRad)
            );

            // 计算圆弧在结束点处的切线方向向量
            // 切线方向是垂直于半径的方向，顺时针为 (-dy, dx)，逆时针为 (dy, -dx)
            SKPoint tangentDirection;
            if (arc.IsCloseWise)
                tangentDirection = new SKPoint(-(float)Math.Sin(endAngleRad), (float)Math.Cos(endAngleRad));
            else tangentDirection = new SKPoint((float)Math.Sin(endAngleRad), -(float)Math.Cos(endAngleRad));


            // 调整为箭头长度的方向向量
            SKPoint arrowVector = new SKPoint(
                -tangentDirection.X * arrowHeadSize,
                -tangentDirection.Y * arrowHeadSize
            );

            // 计算箭头两侧点
            float angleRad = arrowAngle * (float)Math.PI / 180;
            SKPoint left = new SKPoint(
                endPoint.X + arrowVector.X * (float)Math.Cos(angleRad) - arrowVector.Y * (float)Math.Sin(angleRad),
                endPoint.Y + arrowVector.X * (float)Math.Sin(angleRad) + arrowVector.Y * (float)Math.Cos(angleRad)
            );

            SKPoint right = new SKPoint(
                endPoint.X + arrowVector.X * (float)Math.Cos(-angleRad) - arrowVector.Y * (float)Math.Sin(-angleRad),
                endPoint.Y + arrowVector.X * (float)Math.Sin(-angleRad) + arrowVector.Y * (float)Math.Cos(-angleRad)
            );

            var mEnd = ModelToCanvas(new Vector2(endPoint.X, endPoint.Y));
            var mLeft = ModelToCanvas(new Vector2(left.X, left.Y));
            var mRight = ModelToCanvas(new Vector2(right.X, right.Y));


            var vertices = new[] { new SKPoint((float)mEnd.X, (float)mEnd.Y), new SKPoint((float)mLeft.X, (float)mLeft.Y), new SKPoint((float)mRight.X, (float)mRight.Y) };



            var colors = new[] { SKColors.Red.WithAlpha(90), SKColors.Red.WithAlpha(90), SKColors.Red.WithAlpha(90) };
            var skVertices = SKVertices.CreateCopy(SKVertexMode.Triangles, vertices, colors);

            using (var arrowPaint = new SKPaint
            {
                //Color = SKColors.Black,
                IsAntialias = true,
                Style = SKPaintStyle.Fill
            })
            {
                Canvas.DrawVertices(skVertices, SKBlendMode.SrcOver, arrowPaint);
            }
        }

        public void DrawLine(Vector2 p1, Vector2 p2, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) pen = DefaultPen;
            Canvas?.DrawLine((float)p1.X, (float)p1.Y, (float)p2.X, (float)p2.Y, pen);
        }

        public void DrawArc(Vector2 center, double radius, double startAngle, double endAngle, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) return;
            if (endAngle < startAngle)
                endAngle += 360;
            SKRect ovalRect = new SKRect((float)(center.X - radius), (float)(center.Y - radius), (float)(center.X + radius), (float)(center.Y + radius));
            Canvas?.DrawArc(ovalRect, (float)startAngle, (float)(endAngle - startAngle), false, pen);
        }

        public void DrawCircle(Vector2 center, double radius, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) return;
            Canvas?.DrawCircle((float)center.X, (float)center.Y, (float)radius, pen);
        }

        public void DrawCircle(SKCanvas canvas, SKPaint pen, Vector2 center, double radius, CSYS csys = CSYS.Model)
        {
            if (csys == CSYS.Model)
            {
                Vector2 centerInCanvas = ModelToCanvas(center);
                double radiusInCanvas = ModelToCanvas(radius);
                canvas.DrawCircle((float)(centerInCanvas.X - radiusInCanvas), (float)(centerInCanvas.Y - radiusInCanvas), (float)radiusInCanvas * 2, DefaultPen);
            }
            else
            {
                canvas.DrawCircle((float)(center.X - radius), (float)(center.Y - radius), (float)radius * 2, DefaultPen);
            }
        }

        public void DrawRectangle(Vector2 position, double width, double height, SKPaint pen, CSYS csys = CSYS.Model)
        {
            if (pen == null) return;
            Canvas?.DrawRect((float)position.X, (float)position.Y, (float)width, (float)height, pen);
        }

        public void DrawRectangle(Vector2 position, double width, double height, CSYS csys = CSYS.Model)
        {
            SKPaint pen = new SKPaint()
            {
                Color = SKColors.Blue,
                StrokeWidth = 0.6f,
                Style = SKPaintStyle.Stroke,
                IsAntialias = true
            };
            if (csys == CSYS.Model)
            {
                double widthInCanvas = ModelToCanvas(width);
                double heightInCanvas = ModelToCanvas(height);
                var end = ModelToCanvas(new Vector2(position.X + (float)width, position.Y + (float)height));
                Vector2 posInCanvas = ModelToCanvas(position);
                //posInCanvas.Y -= heightInCanvas;
                if (Canvas.Handle == (IntPtr)0)
                {
                    return;
                }

                Canvas.DrawRect((float)posInCanvas.X, (float)posInCanvas.Y, (float)(end.X - posInCanvas.X), -(float)(end.Y - posInCanvas.Y), pen);
            }
            else
            {
                Canvas.DrawRect((float)position.X, (float)position.Y, (float)width, (float)height, pen);
            }
        }

        public void DrawPoint(Vector2 point, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) return;
            pen.Style = SKPaintStyle.Fill;
            if (csys == CSYS.Model)
            {
                Vector2 endInCanvas = ModelToCanvas(point);
                if (Canvas?.Handle == (IntPtr)0)
                {
                    return;
                }

                Canvas?.DrawCircle((float)endInCanvas.X, (float)endInCanvas.Y, 5, pen);
            }
            else
            {
                Canvas?.DrawCircle((float)point.X, (float)point.Y, 5, pen);
            }
        }

        public void DrawPoint(SKCanvas canvas, SKPaint pen, Vector2 point)
        {
            canvas.DrawPoint((float)point.X, (float)point.Y, pen);
        }

        public void DrawCross(Vector2 point, double length, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) pen = DefaultPen;
            if (csys == CSYS.Model)
            {
                Vector2 horizontal1InCanvas = ModelToCanvas(new Vector2(point.X - length / 2, point.Y));
                Vector2 horizontal2InCanvas = ModelToCanvas(new Vector2(point.X + length / 2, point.Y));
                Vector2 vertical1InCanvas = ModelToCanvas(new Vector2(point.X, point.Y + length / 2));
                Vector2 vertical2InCanvas = ModelToCanvas(new Vector2(point.X, point.Y - length / 2));
                if (Canvas.Handle == (IntPtr)0)
                {
                    return;
                }
                Canvas?.DrawLine((float)horizontal1InCanvas.X, (float)horizontal1InCanvas.Y, (float)horizontal2InCanvas.X, (float)horizontal2InCanvas.Y, pen);
                Canvas?.DrawLine((float)vertical1InCanvas.X, (float)vertical1InCanvas.Y, (float)vertical2InCanvas.X, (float)vertical2InCanvas.Y, pen);
            }
            else
            {
                Vector2 horizontal1 = new Vector2(point.X - length / 2, point.Y);
                Vector2 horizontal2 = new Vector2(point.X + length / 2, point.Y);
                Vector2 vertical1 = new Vector2(point.X, point.Y + length / 2);
                Vector2 vertical2 = new Vector2(point.X, point.Y - length / 2);
                Canvas?.DrawLine((float)horizontal1.X, (float)horizontal1.Y, (float)horizontal2.X, (float)horizontal2.Y, pen);
                Canvas?.DrawLine((float)vertical1.X, (float)vertical1.Y, (float)vertical2.X, (float)vertical2.Y, pen);
            }
        }

        public void DrawText(string text, Vector2 point, SKPaint pen = null, SKPaint rectPen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) return;
            SKRect rect = new SKRect();
            pen.MeasureText(text, ref rect);
            if (csys == CSYS.Model)
            {
                Vector2 endInCanvas = ModelToCanvas(point);
                if (Canvas?.Handle == (IntPtr)0)
                {
                    return;
                }
                Canvas?.DrawRect((float)(rect.Left + endInCanvas.X - 5), (float)(endInCanvas.Y + rect.Top - 5), rect.Width + 10, rect.Height + 10, rectPen);
                Canvas?.DrawText(text, (float)endInCanvas.X, (float)endInCanvas.Y, pen);
            }
            else
            {
                Canvas?.DrawRect((float)(rect.Left + point.X), (float)(point.Y + rect.Top), rect.Width, rect.Height, rectPen);
                Canvas?.DrawText(text, (float)point.X, (float)point.Y, pen);
            }
        }


        public float ModelToCanvas(double value)
        {
            //return (float)value / _viewMatrix.ScaleX;
            return (_viewMatrix.MapPoint(0, (float)value).Y) - (_viewMatrix.MapPoint(0, 0).Y);
        }

        public Vector2 ModelToCanvas(Vector2 pointInModel)
        {

            var pt = _viewMatrix.MapPoint(new SKPoint((float)pointInModel.X, (float)pointInModel.Y));

            return new Vector2(pt.X, pt.Y);
        }

        public float CanvasToModel(double value)
        {
            return _viewMatrix.Invert().MapPoint((float)value, 0).X;
        }

        public Vector2 CanvasToModel(Vector2 pointInCanvas)
        {
            var pt = _viewMatrix.Invert().MapPoint((float)pointInCanvas.X, (float)pointInCanvas.Y);
            return new Vector2(pt.X, pt.Y);
        }

        public void Dispose()
        {
            //this.Dispose(true);
            GC.SuppressFinalize(this);
        }

    }
}
