﻿using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Input
{
    /// <summary>
    /// 命令输入控件
    /// </summary>
    public class DynInputString : DynInputTextBoxOne<string>
    {
        /// <summary>
        /// 更新值
        /// </summary>
        protected override bool UpdateValue()
        {
            _value = _textBox.Text;
            return true;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public DynInputString(ViewBase presenter, string value)
            : base(presenter, value)
        {
        }
    }
}
