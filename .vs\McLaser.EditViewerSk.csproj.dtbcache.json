{"RootPath": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk", "ProjectFileName": "McLaser.EditViewerSk.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Base\\Command.cs"}, {"SourceFile": "Base\\Config.cs"}, {"SourceFile": "Base\\CSYS.cs"}, {"SourceFile": "Base\\IconCache.cs"}, {"SourceFile": "Behaviors\\BindableTreeViewSelectedItemBehavior.cs"}, {"SourceFile": "Convertors\\BooleanTypeDescriptionProvider.cs"}, {"SourceFile": "Convertors\\BooleanTypeDescriptor.cs"}, {"SourceFile": "Convertors\\CheckBoxEditor.cs"}, {"SourceFile": "Convertors\\LongTextEditor.cs"}, {"SourceFile": "Convertors\\PropertyGridWrapper.cs"}, {"SourceFile": "Entitys\\EntityBase.cs"}, {"SourceFile": "Base\\GripPoint.cs"}, {"SourceFile": "Base\\LineType.cs"}, {"SourceFile": "Base\\LineWeight.cs"}, {"SourceFile": "Base\\LocateCross.cs"}, {"SourceFile": "Base\\ObjectId.cs"}, {"SourceFile": "Base\\ObjectSnapMode.cs"}, {"SourceFile": "Base\\ObjectSnapPoint.cs"}, {"SourceFile": "Base\\PickupBox.cs"}, {"SourceFile": "Commands\\ArcCmd.cs"}, {"SourceFile": "Commands\\CircleCmd.cs"}, {"SourceFile": "Commands\\LineCmd.cs"}, {"SourceFile": "Commands\\PointCmd.cs"}, {"SourceFile": "Commands\\PolylineCmd.cs"}, {"SourceFile": "Commands\\RectangleCmd.cs"}, {"SourceFile": "Common\\AsyncCommand.cs"}, {"SourceFile": "Common\\MgrIndicator.cs"}, {"SourceFile": "Base\\SelectRectangle.cs"}, {"SourceFile": "Base\\TableIds.cs"}, {"SourceFile": "Commands\\DeleteCmd.cs"}, {"SourceFile": "Commands\\DrawCmd.cs"}, {"SourceFile": "Commands\\EditCmd.cs"}, {"SourceFile": "Commands\\GripPointMoveCmd.cs"}, {"SourceFile": "Commands\\LinesChainCmd.cs"}, {"SourceFile": "Commands\\MirrorCmd.cs"}, {"SourceFile": "Commands\\ModifyCmd.cs"}, {"SourceFile": "Commands\\MoveCmd.cs"}, {"SourceFile": "Commands\\OffsetCmd.cs"}, {"SourceFile": "Common\\CommandsFactory.cs"}, {"SourceFile": "Common\\CommandsMgr.cs"}, {"SourceFile": "Base\\DocumentBase.cs"}, {"SourceFile": "Base\\ViewBase.cs"}, {"SourceFile": "Commands\\RedoCmd.cs"}, {"SourceFile": "Commands\\UndoCmd.cs"}, {"SourceFile": "Common\\AnchorsMgr.cs"}, {"SourceFile": "Common\\GDIResMgr.cs"}, {"SourceFile": "Common\\MathUtils.cs"}, {"SourceFile": "Common\\ObjectIdMgr.cs"}, {"SourceFile": "Common\\MgrSnap.cs"}, {"SourceFile": "Common\\ObservableObject.cs"}, {"SourceFile": "Common\\RelayCommand.cs"}, {"SourceFile": "Common\\XmlFiler.cs"}, {"SourceFile": "Dxf\\AciColor.cs"}, {"SourceFile": "Dxf\\BezierCurve.cs"}, {"SourceFile": "Dxf\\BezierCurveCubic.cs"}, {"SourceFile": "Dxf\\BezierCurveQuadratic.cs"}, {"SourceFile": "Dxf\\Blocks\\Block.cs"}, {"SourceFile": "Dxf\\Blocks\\BlockAttributeDefinitionChangeEventArgs.cs"}, {"SourceFile": "Dxf\\Blocks\\BlockEntityChangeEventArgs.cs"}, {"SourceFile": "Dxf\\Blocks\\BlockRecord.cs"}, {"SourceFile": "Dxf\\Blocks\\BlockTypeFlags.cs"}, {"SourceFile": "Dxf\\Blocks\\EndBlock.cs"}, {"SourceFile": "Dxf\\BoundingRectangle.cs"}, {"SourceFile": "Dxf\\ClippingBoundary.cs"}, {"SourceFile": "Dxf\\ClippingBoundaryType.cs"}, {"SourceFile": "Dxf\\Collections\\ApplicationRegistries.cs"}, {"SourceFile": "Dxf\\Collections\\AttributeCollection.cs"}, {"SourceFile": "Dxf\\Collections\\AttributeDefinitionDictionary.cs"}, {"SourceFile": "Dxf\\Collections\\AttributeDefinitionDictionaryEventArgs.cs"}, {"SourceFile": "Dxf\\Collections\\BlockRecords.cs"}, {"SourceFile": "Dxf\\Collections\\DimensionStyleOverrideDictionary.cs"}, {"SourceFile": "Dxf\\Collections\\DimensionStyleOverrideDictionaryEventArgs.cs"}, {"SourceFile": "Dxf\\Collections\\DimensionStyles.cs"}, {"SourceFile": "Dxf\\Collections\\DrawingEntities.cs"}, {"SourceFile": "Dxf\\Collections\\DxfObjectReferences.cs"}, {"SourceFile": "Dxf\\Collections\\EntityCollection.cs"}, {"SourceFile": "Dxf\\Collections\\EntityCollectionEventArgs.cs"}, {"SourceFile": "Dxf\\Collections\\Groups.cs"}, {"SourceFile": "Dxf\\Collections\\ImageDefinitions.cs"}, {"SourceFile": "Dxf\\Collections\\Layers.cs"}, {"SourceFile": "Dxf\\Collections\\LayerStateManager.cs"}, {"SourceFile": "Dxf\\Collections\\Layouts.cs"}, {"SourceFile": "Dxf\\Collections\\Linetypes.cs"}, {"SourceFile": "Dxf\\Collections\\MLineStyles.cs"}, {"SourceFile": "Dxf\\Collections\\ObservableCollection.cs"}, {"SourceFile": "Dxf\\Collections\\ObservableCollectionEventArgs.cs"}, {"SourceFile": "Dxf\\Collections\\ObservableDictionary.cs"}, {"SourceFile": "Dxf\\Collections\\ObservableDictionaryEventArgs.cs"}, {"SourceFile": "Dxf\\Collections\\ShapeStyles.cs"}, {"SourceFile": "Dxf\\Collections\\SupportFolders.cs"}, {"SourceFile": "Dxf\\Collections\\TableObjects.cs"}, {"SourceFile": "Dxf\\Collections\\TextStyles.cs"}, {"SourceFile": "Dxf\\Collections\\UCSs.cs"}, {"SourceFile": "Dxf\\Collections\\UnderlayDgnDefinitions.cs"}, {"SourceFile": "Dxf\\Collections\\UnderlayDwfDefinitions.cs"}, {"SourceFile": "Dxf\\Collections\\UnderlayPdfDefinitions.cs"}, {"SourceFile": "Dxf\\Collections\\Views.cs"}, {"SourceFile": "Dxf\\Collections\\VPorts.cs"}, {"SourceFile": "Dxf\\Collections\\XDataDictionary.cs"}, {"SourceFile": "Dxf\\CoordinateSystem.cs"}, {"SourceFile": "Dxf\\DxfDocument.cs"}, {"SourceFile": "Dxf\\DxfObject.cs"}, {"SourceFile": "Dxf\\DxfObjectCode.cs"}, {"SourceFile": "Dxf\\DxfObjectReference.cs"}, {"SourceFile": "Dxf\\Entities\\AlignedDimension.cs"}, {"SourceFile": "Dxf\\Entities\\Angular2LineDimension.cs"}, {"SourceFile": "Dxf\\Entities\\Angular3PointDimension.cs"}, {"SourceFile": "Dxf\\Entities\\Arc.cs"}, {"SourceFile": "Dxf\\Entities\\ArcLengthDimension.cs"}, {"SourceFile": "Dxf\\Entities\\Attribute.cs"}, {"SourceFile": "Dxf\\Entities\\AttributeChangeEventArgs.cs"}, {"SourceFile": "Dxf\\Entities\\AttributeDefinition.cs"}, {"SourceFile": "Dxf\\Entities\\AttributeFlags.cs"}, {"SourceFile": "Dxf\\Entities\\Circle.cs"}, {"SourceFile": "Dxf\\Entities\\DatumReferenceValue.cs"}, {"SourceFile": "Dxf\\Entities\\DiametricDimension.cs"}, {"SourceFile": "Dxf\\Entities\\Dimension.cs"}, {"SourceFile": "Dxf\\Entities\\DimensionArrowhead.cs"}, {"SourceFile": "Dxf\\Entities\\DimensionBlock.cs"}, {"SourceFile": "Dxf\\Entities\\DimensionType.cs"}, {"SourceFile": "Dxf\\Entities\\DimensionTypeFlags.cs"}, {"SourceFile": "Dxf\\Entities\\Ellipse.cs"}, {"SourceFile": "Dxf\\Entities\\EndSequence.cs"}, {"SourceFile": "Dxf\\Entities\\EntityChangeEventArgs.cs"}, {"SourceFile": "Dxf\\Entities\\EntityObject.cs"}, {"SourceFile": "Dxf\\Entities\\EntityType.cs"}, {"SourceFile": "Dxf\\Entities\\Face3D.cs"}, {"SourceFile": "Dxf\\Entities\\Face3DEdgeFlags.cs"}, {"SourceFile": "Dxf\\Entities\\Hatch.cs"}, {"SourceFile": "Dxf\\Entities\\HatchBoundaryPath.cs"}, {"SourceFile": "Dxf\\Entities\\HatchBoundaryPathTypeFlags.cs"}, {"SourceFile": "Dxf\\Entities\\HatchFillType.cs"}, {"SourceFile": "Dxf\\Entities\\HatchGradientPattern.cs"}, {"SourceFile": "Dxf\\Entities\\HatchGradientPatternType.cs"}, {"SourceFile": "Dxf\\Entities\\HatchPattern.cs"}, {"SourceFile": "Dxf\\Entities\\HatchPatternLineDefinition.cs"}, {"SourceFile": "Dxf\\Entities\\HatchStyle.cs"}, {"SourceFile": "Dxf\\Entities\\HatchType.cs"}, {"SourceFile": "Dxf\\Entities\\Image.cs"}, {"SourceFile": "Dxf\\Entities\\ImageDisplayFlags.cs"}, {"SourceFile": "Dxf\\Entities\\Insert.cs"}, {"SourceFile": "Dxf\\Entities\\Leader.cs"}, {"SourceFile": "Dxf\\Entities\\LeaderPathType.cs"}, {"SourceFile": "Dxf\\Entities\\Line.cs"}, {"SourceFile": "Dxf\\Entities\\LinearDimension.cs"}, {"SourceFile": "Dxf\\Entities\\Mesh.cs"}, {"SourceFile": "Dxf\\Entities\\MeshEdge.cs"}, {"SourceFile": "Dxf\\Entities\\MLine.cs"}, {"SourceFile": "Dxf\\Entities\\MLineFlags.cs"}, {"SourceFile": "Dxf\\Entities\\MLineJustification.cs"}, {"SourceFile": "Dxf\\Entities\\MLineVertex.cs"}, {"SourceFile": "Dxf\\Entities\\MText.cs"}, {"SourceFile": "Dxf\\Entities\\MTextAttachmentPoint.cs"}, {"SourceFile": "Dxf\\Entities\\MTextDrawingDirection.cs"}, {"SourceFile": "Dxf\\Entities\\MTextFormattingOptions.cs"}, {"SourceFile": "Dxf\\Entities\\MTextLineSpacingStyle.cs"}, {"SourceFile": "Dxf\\Entities\\MTextParagraphAlignment.cs"}, {"SourceFile": "Dxf\\Entities\\MTextParagraphOptions.cs"}, {"SourceFile": "Dxf\\Entities\\MTextParagraphVerticalAlignment.cs"}, {"SourceFile": "Dxf\\Entities\\OrdinateDimension.cs"}, {"SourceFile": "Dxf\\Entities\\OrdinateDimensionAxis.cs"}, {"SourceFile": "Dxf\\Entities\\Point.cs"}, {"SourceFile": "Dxf\\Entities\\PolyfaceMesh.cs"}, {"SourceFile": "Dxf\\Entities\\PolyfaceMeshFace.cs"}, {"SourceFile": "Dxf\\Entities\\PolygonMesh.cs"}, {"SourceFile": "Dxf\\Entities\\Polyline.cs"}, {"SourceFile": "Dxf\\Entities\\Polyline2D.cs"}, {"SourceFile": "Dxf\\Entities\\Polyline2DVertex.cs"}, {"SourceFile": "Dxf\\Entities\\Polyline3D.cs"}, {"SourceFile": "Dxf\\Entities\\PolylineSmoothType.cs"}, {"SourceFile": "Dxf\\Entities\\PolylineTypeFlags.cs"}, {"SourceFile": "Dxf\\Entities\\RadialDimension.cs"}, {"SourceFile": "Dxf\\Entities\\Ray.cs"}, {"SourceFile": "Dxf\\Entities\\Shape.cs"}, {"SourceFile": "Dxf\\Entities\\Solid.cs"}, {"SourceFile": "Dxf\\Entities\\Spline.cs"}, {"SourceFile": "Dxf\\Entities\\SplineCreationMethod.cs"}, {"SourceFile": "Dxf\\Entities\\SplineKnotParameterization.cs"}, {"SourceFile": "Dxf\\Entities\\SplineTypeFlags.cs"}, {"SourceFile": "Dxf\\Entities\\Text.cs"}, {"SourceFile": "Dxf\\Entities\\TextAligment.cs"}, {"SourceFile": "Dxf\\Entities\\Tolerance.cs"}, {"SourceFile": "Dxf\\Entities\\ToleranceEntry.cs"}, {"SourceFile": "Dxf\\Entities\\ToleranceGeometricSymbol.cs"}, {"SourceFile": "Dxf\\Entities\\ToleranceMaterialCondition.cs"}, {"SourceFile": "Dxf\\Entities\\ToleranceValue.cs"}, {"SourceFile": "Dxf\\Entities\\Trace.cs"}, {"SourceFile": "Dxf\\Entities\\Underlay.cs"}, {"SourceFile": "Dxf\\Entities\\UnderlayDisplayFlags.cs"}, {"SourceFile": "Dxf\\Entities\\Vertex.cs"}, {"SourceFile": "Dxf\\Entities\\VertexTypeFlags.cs"}, {"SourceFile": "Dxf\\Entities\\Viewport.cs"}, {"SourceFile": "Dxf\\Entities\\ViewportStatusFlags.cs"}, {"SourceFile": "Dxf\\Entities\\Wipeout.cs"}, {"SourceFile": "Dxf\\Entities\\XLine.cs"}, {"SourceFile": "Dxf\\GTE\\BandedMatrix.cs"}, {"SourceFile": "Dxf\\GTE\\BasisFunction.cs"}, {"SourceFile": "Dxf\\GTE\\BezierCurve.cs"}, {"SourceFile": "Dxf\\GTE\\BSplineCurve.cs"}, {"SourceFile": "Dxf\\GTE\\BSplineCurveFit.cs"}, {"SourceFile": "Dxf\\GTE\\BSplineReduction.cs"}, {"SourceFile": "Dxf\\GTE\\BSplineSurface.cs"}, {"SourceFile": "Dxf\\GTE\\BSplineSurfaceFit.cs"}, {"SourceFile": "Dxf\\GTE\\GaussianElimination.cs"}, {"SourceFile": "Dxf\\GTE\\GMatrix.cs"}, {"SourceFile": "Dxf\\GTE\\GTE.cs"}, {"SourceFile": "Dxf\\GTE\\GVector.cs"}, {"SourceFile": "Dxf\\GTE\\Integration.cs"}, {"SourceFile": "Dxf\\GTE\\IntrIntervals.cs"}, {"SourceFile": "Dxf\\GTE\\LexicoArray2.cs"}, {"SourceFile": "Dxf\\GTE\\NURBSCurve.cs"}, {"SourceFile": "Dxf\\GTE\\NURBSSurface.cs"}, {"SourceFile": "Dxf\\GTE\\ParametricCurve.cs"}, {"SourceFile": "Dxf\\GTE\\ParametricSurface.cs"}, {"SourceFile": "Dxf\\GTE\\RootBisection.cs"}, {"SourceFile": "Dxf\\GTE\\RootsPolynominal.cs"}, {"SourceFile": "Dxf\\Header\\AttMode.cs"}, {"SourceFile": "Dxf\\Header\\DxfVersion.cs"}, {"SourceFile": "Dxf\\Header\\HeaderVariable.cs"}, {"SourceFile": "Dxf\\Header\\HeaderVariableCode.cs"}, {"SourceFile": "Dxf\\Header\\HeaderVariables.cs"}, {"SourceFile": "Dxf\\Header\\PointShape.cs"}, {"SourceFile": "Dxf\\IO\\BinaryCodeValueReader.cs"}, {"SourceFile": "Dxf\\IO\\BinaryCodeValueWriter.cs"}, {"SourceFile": "Dxf\\IO\\DxfReader.cs"}, {"SourceFile": "Dxf\\IO\\DxfVersionNotSupportedException.cs"}, {"SourceFile": "Dxf\\IO\\DxfWriter.cs"}, {"SourceFile": "Dxf\\IO\\ICodeValueReader.cs"}, {"SourceFile": "Dxf\\IO\\ICodeValueWriter.cs"}, {"SourceFile": "Dxf\\IO\\TextCodeValueReader.cs"}, {"SourceFile": "Dxf\\IO\\TextCodeValueWriter.cs"}, {"SourceFile": "Dxf\\Lineweight.cs"}, {"SourceFile": "Dxf\\MathHelper.cs"}, {"SourceFile": "Dxf\\Matrix2.cs"}, {"SourceFile": "Dxf\\Matrix3.cs"}, {"SourceFile": "Dxf\\Matrix4.cs"}, {"SourceFile": "Dxf\\Objects\\DictionaryCloningFlags.cs"}, {"SourceFile": "Dxf\\Objects\\DictionaryObject.cs"}, {"SourceFile": "Dxf\\Objects\\Group.cs"}, {"SourceFile": "Dxf\\Objects\\GroupEntityChangeEventArgs.cs"}, {"SourceFile": "Dxf\\Objects\\ImageDefinition.cs"}, {"SourceFile": "Dxf\\Objects\\ImageDefinitionReactor.cs"}, {"SourceFile": "Dxf\\Objects\\ImageDisplayQuality.cs"}, {"SourceFile": "Dxf\\Objects\\LayerPropertiesFlags.cs"}, {"SourceFile": "Dxf\\Objects\\LayerPropertiesRestoreFlags.cs"}, {"SourceFile": "Dxf\\Objects\\LayerState.cs"}, {"SourceFile": "Dxf\\Objects\\LayerStateProperties.cs"}, {"SourceFile": "Dxf\\Objects\\Layout.cs"}, {"SourceFile": "Dxf\\Objects\\MLineStyle.cs"}, {"SourceFile": "Dxf\\Objects\\MLineStyleElement.cs"}, {"SourceFile": "Dxf\\Objects\\MLineStyleElementChangeEventArgs.cs"}, {"SourceFile": "Dxf\\Objects\\MLineStyleFlags.cs"}, {"SourceFile": "Dxf\\Objects\\PaperMargin.cs"}, {"SourceFile": "Dxf\\Objects\\PlotFlags.cs"}, {"SourceFile": "Dxf\\Objects\\PlotPaperUnits.cs"}, {"SourceFile": "Dxf\\Objects\\PlotRotation.cs"}, {"SourceFile": "Dxf\\Objects\\PlotSettings.cs"}, {"SourceFile": "Dxf\\Objects\\PlotType.cs"}, {"SourceFile": "Dxf\\Objects\\RasterVariables.cs"}, {"SourceFile": "Dxf\\Objects\\ShadePlotMode.cs"}, {"SourceFile": "Dxf\\Objects\\ShadePlotResolutionMode.cs"}, {"SourceFile": "Dxf\\Objects\\SupportedImageFormats.cs"}, {"SourceFile": "Dxf\\Objects\\UnderlayDefinition.cs"}, {"SourceFile": "Dxf\\Objects\\UnderlayDgnDefinition.cs"}, {"SourceFile": "Dxf\\Objects\\UnderlayDwfDefinition.cs"}, {"SourceFile": "Dxf\\Objects\\UnderlayPdfDefinition.cs"}, {"SourceFile": "Dxf\\Objects\\UnderlayType.cs"}, {"SourceFile": "Dxf\\Objects\\XRecord.cs"}, {"SourceFile": "Dxf\\Objects\\XRecordEntry.cs"}, {"SourceFile": "Dxf\\StringEnum.cs"}, {"SourceFile": "Dxf\\SubclassMarker.cs"}, {"SourceFile": "Dxf\\Symbols.cs"}, {"SourceFile": "Dxf\\Tables\\ApplicationRegistry.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyle.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleAlternateUnits.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleFitOptions.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleFitTextMove.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleOverride.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleOverrideChangeEventArgs.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleOverrideType.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleTextDirection.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleTextHorizontalPlacement.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleTextVerticalPlacement.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleTolerances.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleTolerancesDisplayMethod.cs"}, {"SourceFile": "Dxf\\Tables\\DimensionStyleTolerancesVerticalPlacement.cs"}, {"SourceFile": "Dxf\\Tables\\FontStyle.cs"}, {"SourceFile": "Dxf\\Tables\\Layer.cs"}, {"SourceFile": "Dxf\\Tables\\LayerFlags.cs"}, {"SourceFile": "Dxf\\Tables\\Linetype.cs"}, {"SourceFile": "Dxf\\Tables\\LinetypeSegment.cs"}, {"SourceFile": "Dxf\\Tables\\LinetypeSegmentChangeEventArgs.cs"}, {"SourceFile": "Dxf\\Tables\\LinetypeSegmentRotationType.cs"}, {"SourceFile": "Dxf\\Tables\\LinetypeSegmentType.cs"}, {"SourceFile": "Dxf\\Tables\\LinetypeShapeSegment.cs"}, {"SourceFile": "Dxf\\Tables\\LinetypeSimpleSegment.cs"}, {"SourceFile": "Dxf\\Tables\\LinetypeTextSegment.cs"}, {"SourceFile": "Dxf\\Tables\\ShapeStyle.cs"}, {"SourceFile": "Dxf\\Tables\\TableObject.cs"}, {"SourceFile": "Dxf\\Tables\\TableObjectChangedEventArgs.cs"}, {"SourceFile": "Dxf\\Tables\\TextStyle.cs"}, {"SourceFile": "Dxf\\Tables\\UCS.cs"}, {"SourceFile": "Dxf\\Tables\\View.cs"}, {"SourceFile": "Dxf\\Tables\\ViewMode.cs"}, {"SourceFile": "Dxf\\Tables\\VPort.cs"}, {"SourceFile": "Dxf\\Transparency.cs"}, {"SourceFile": "Dxf\\Units\\AngleDirection.cs"}, {"SourceFile": "Dxf\\Units\\AngleUnitFormat.cs"}, {"SourceFile": "Dxf\\Units\\AngleUnitType.cs"}, {"SourceFile": "Dxf\\Units\\DrawingTime.cs"}, {"SourceFile": "Dxf\\Units\\DrawingUnits.cs"}, {"SourceFile": "Dxf\\Units\\FractionFormatType.cs"}, {"SourceFile": "Dxf\\Units\\ImageResolutionUnits.cs"}, {"SourceFile": "Dxf\\Units\\ImageUnits.cs"}, {"SourceFile": "Dxf\\Units\\LinearUnitFormat.cs"}, {"SourceFile": "Dxf\\Units\\LinearUnitType.cs"}, {"SourceFile": "Dxf\\Units\\UnitHelper.cs"}, {"SourceFile": "Dxf\\Units\\UnitStyleFormat.cs"}, {"SourceFile": "Dxf\\Vector2.cs"}, {"SourceFile": "Dxf\\Vector3.cs"}, {"SourceFile": "Dxf\\Vector4.cs"}, {"SourceFile": "Dxf\\XData.cs"}, {"SourceFile": "Dxf\\XDataCode.cs"}, {"SourceFile": "Dxf\\XDataRecord.cs"}, {"SourceFile": "Entitys\\BoundingBox.cs"}, {"SourceFile": "Entitys\\EntityArc.cs"}, {"SourceFile": "Entitys\\EntityBlock.cs"}, {"SourceFile": "Entitys\\EntityCircle.cs"}, {"SourceFile": "Entitys\\EntityCmd.cs"}, {"SourceFile": "Entitys\\EntityGroup.cs"}, {"SourceFile": "Entitys\\EntityLayer.cs"}, {"SourceFile": "Entitys\\EntityLine.cs"}, {"SourceFile": "Entitys\\EntityPen.cs"}, {"SourceFile": "Entitys\\EntityPoint.cs"}, {"SourceFile": "Entitys\\EntityPolyline2D.cs"}, {"SourceFile": "Entitys\\EntitySpline.cs"}, {"SourceFile": "Entitys\\EntityLwPolyline.cs"}, {"SourceFile": "Entitys\\EntityRectangle.cs"}, {"SourceFile": "Entitys\\EntityText.cs"}, {"SourceFile": "Entitys\\IEntity.cs"}, {"SourceFile": "Entitys\\LwPolyLineVertex.cs"}, {"SourceFile": "Entitys\\Offset.cs"}, {"SourceFile": "ImageExtension.cs"}, {"SourceFile": "Input\\DynamicInputer.cs"}, {"SourceFile": "Input\\DynamicInputTextBox.cs"}, {"SourceFile": "Input\\DynInputCtrl.cs"}, {"SourceFile": "Input\\DynInputDouble.cs"}, {"SourceFile": "Input\\DynInputInteger.cs"}, {"SourceFile": "Input\\DynInputPoint.cs"}, {"SourceFile": "Input\\DynInputResult.cs"}, {"SourceFile": "Input\\DynInputStatus.cs"}, {"SourceFile": "Input\\DynInputString.cs"}, {"SourceFile": "Input\\DynInputTextBoxOne.cs"}, {"SourceFile": "Interfaces\\ICanvas.cs"}, {"SourceFile": "Interfaces\\ICommand.cs"}, {"SourceFile": "Interfaces\\IDocument.cs"}, {"SourceFile": "Core\\IEntity.cs"}, {"SourceFile": "Interfaces\\IGraphicsContext.cs"}, {"SourceFile": "Interfaces\\IMarkerable.cs"}, {"SourceFile": "Interfaces\\IRenderable.cs"}, {"SourceFile": "Interfaces\\IView.cs"}, {"SourceFile": "Interfaces\\TextAlignment.cs"}, {"SourceFile": "Marker\\ILaser.cs"}, {"SourceFile": "Marker\\IMark.cs"}, {"SourceFile": "Marker\\IMarker.cs"}, {"SourceFile": "Marker\\IMarkerArg.cs"}, {"SourceFile": "Marker\\IMatrixStack.cs"}, {"SourceFile": "Marker\\LaserVirtual.cs"}, {"SourceFile": "Marker\\MarkArg.cs"}, {"SourceFile": "Marker\\MarkerArgDefault.cs"}, {"SourceFile": "Marker\\MarkerArgPmac.cs"}, {"SourceFile": "Marker\\MarkerCoordinate.cs"}, {"SourceFile": "Marker\\MarkerPmac.cs"}, {"SourceFile": "Marker\\MatrixStack.cs"}, {"SourceFile": "UndoRedo\\IUndoRedo.cs"}, {"SourceFile": "UndoRedo\\UndoRedoEntityAdd.cs"}, {"SourceFile": "Action.cs"}, {"SourceFile": "UndoRedo\\UndoRedoEntityDelete.cs"}, {"SourceFile": "UndoRedo\\UndoRedoEntityGroup.cs"}, {"SourceFile": "UndoRedo\\UndoRedoEntityMove.cs"}, {"SourceFile": "UndoRedo\\UndoRedoEntitySort.cs"}, {"SourceFile": "UndoRedo\\UndoRedoEntityReverse.cs"}, {"SourceFile": "UndoRedo\\UndoRedoEntityUnGroup.cs"}, {"SourceFile": "UndoRedo\\UndoRedoMultiple.cs"}, {"SourceFile": "UndoRedo\\UndoRedoSingle.cs"}, {"SourceFile": "ViewModels\\CardSettingWindowViewModel.cs"}, {"SourceFile": "ViewModels\\MarkerSettingWindowViewModel.cs"}, {"SourceFile": "ViewModels\\MarkingViewModel.cs"}, {"SourceFile": "ViewModels\\SkCanvasViewModel.cs"}, {"SourceFile": "Views\\BufferView.xaml.cs"}, {"SourceFile": "Views\\CardSettingWindow.xaml.cs"}, {"SourceFile": "Views\\CustomPropertyGrid.cs"}, {"SourceFile": "Views\\GLEditor.cs"}, {"SourceFile": "Views\\GLEditor.Designer.cs"}, {"SourceFile": "Views\\SettingWindow.xaml.cs"}, {"SourceFile": "Views\\MarkingView.xaml.cs"}, {"SourceFile": "Views\\OptimizerWindow.xaml.cs"}, {"SourceFile": "Views\\SkEditor.xaml.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\obj\\Debug\\Views\\BufferView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\obj\\Debug\\Views\\CardSettingWindow.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\obj\\Debug\\Views\\SettingWindow.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\obj\\Debug\\Views\\MarkingView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\obj\\Debug\\Views\\OptimizerWindow.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\obj\\Debug\\Views\\SkEditor.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\obj\\Debug\\GeneratedInternalTypeHelper.g.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\bin\\Debug\\McLaser.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\packages\\Microsoft.Xaml.Behaviors.Wpf.1.1.135\\lib\\net462\\Microsoft.Xaml.Behaviors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Device\\ModuleBase\\bin\\Debug\\ModuleBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Device\\ModuleBase\\bin\\Debug\\ModuleBase.dll"}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Device\\ModuleMotion\\bin\\Debug\\ModuleMotion.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Device\\ModuleMotion\\bin\\Debug\\ModuleMotion.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\bin\\Debug\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\packages\\OpenTK.3.1.0\\lib\\net20\\OpenTK.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\packages\\OpenTK.GLControl.3.1.0\\lib\\net20\\OpenTK.GLControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\bin\\Debug\\PropertyTools.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\bin\\Debug\\PropertyTools.Wpf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\packages\\SkiaSharp.3.116.1\\lib\\net462\\SkiaSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\packages\\SkiaSharp.Views.Desktop.Common.3.116.1\\lib\\net462\\SkiaSharp.Views.Desktop.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\packages\\SkiaSharp.Views.WindowsForms.3.116.1\\lib\\net462\\SkiaSharp.Views.WindowsForms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\bin\\Debug\\SkiaSharp.Views.WPF.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\PublicAssemblies\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\bin\\Debug\\System.Drawing.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\PublicAssemblies\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\PublicAssemblies\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\bin\\Debug\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\WindowsFormsIntegration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\伯恩倒角\\src 2025.01.23三星双工位倒角\\Draw\\McLaser.EditViewerSk\\bin\\Debug\\McLaser.EditViewerSk.dll", "OutputItemRelativePath": "McLaser.EditViewerSk.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}