﻿using System;
using System.CodeDom;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Marker
{
    //二轴坐标系 
    //如果是三轴、四轴、多轴坐标系呢？？图纸系统现在无法兼容，只能兼容2D
    public class MarkerCoordinate2D
    {
        public int AxisXIndex = 0;
        public int AxisYIndex = 1;
        public int AxisZIndex = 2;

        public string AxisXName = "X";
        public string AxisYName = "Y";
        public string AxisZName = "Z";

        public MarkerCoordinate2D(int x, string x_name, int y, string y_name)
        {
            AxisXIndex = x; 
            AxisYIndex = y; 
            AxisXName = x_name; 
            AxisYName = y_name;
        }

        public MarkerCoordinate2D(int x, string x_name, int y, string y_name, int z, string z_name)
        {
            AxisXIndex = x;
            AxisYIndex = y;
            AxisZIndex = z;
            AxisXName = x_name;
            AxisYName = y_name;
            AxisZName = z_name;
        }

        public MarkerCoordinate2D()
        {
                    
        }

    }
}
