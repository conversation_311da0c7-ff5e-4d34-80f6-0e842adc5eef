﻿using McLaser.EditViewerSk.Common;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Numerics;

namespace McLaser.EditViewerSk.Base
{
    /// <summary>
    /// 选择矩形
    /// Canvas坐标
    /// </summary>
    public class SelectRectangle
    {
        private ViewBase _presenter = null;

        /// <summary>
        /// 选择模式
        /// </summary>
        internal enum SelectMode
        {
            // 闭合框选,物体完全在选择矩形框内则选中物体
            Window = 1,
            // 交叉框选,物体与选择矩形框有交集则选中物体
            Cross = 2,
        }

        /// <summary>
        /// 矩形对角起点
        /// Canvas CSYS
        /// </summary>
        private Vector2 _startPoint = new Vector2(0, 0);
        internal Vector2 startPoint
        {
            get { return _startPoint; }
            set { _startPoint = value; }
        }

        /// <summary>
        /// 矩形对角终点
        /// Canvas CSYS
        /// </summary>
        private Vector2 _endPoint = new Vector2(0, 0);
        internal Vector2 endPoint
        {
            get { return _endPoint; }
            set { _endPoint = value; }
        }

        /// <summary>
        /// 选择模式
        /// </summary>
        internal SelectMode selectMode
        {
            get
            {
                if (_endPoint.X >= _startPoint.X)
                {
                    return SelectMode.Window;
                }
                else
                {
                    return SelectMode.Cross;
                }
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        internal SelectRectangle(ViewBase presenter)
        {
            _presenter = presenter;
        }

        /// <summary>
        /// 绘制
        /// </summary> 
        internal void OnPaint(SKCanvas canvas)
        {
            //IntPtr pen;
            //if (this.selectMode == SelectRectangle.SelectMode.Window)
            //{
            //    pen = GDIResMgr.Instance.selectWindowPen;
            //}
            //else
            //{
            //    pen = GDIResMgr.Instance.selectCrossPen;
            //}
            //SKPaint paint = new SKPaint();
            //canvas.DrawRect((float)_startPoint.X, (float)_startPoint.Y, (float)(_endPoint.X-_startPoint.X), (float)(_endPoint.Y - _startPoint.Y), paint);
        }

        /// <summary>
        /// 选择
        /// </summary>
        //internal List<Selection> Select(Block block)
        //{
        //    Bounding selectBound = new Bounding(
        //        _presenter.CanvasToModel(_startPoint),
        //        _presenter.CanvasToModel(_endPoint));

        //    List<Selection> sels = new List<Selection>();
        //    SelectMode selMode = this.selectMode;
        //    foreach (Entity entity in block)
        //    {
        //        bool selected = false;
        //        if (selMode == SelectMode.Cross)
        //        {
        //            selected = EntityRSMgr.Instance.Cross(selectBound, entity);
        //        }
        //        else if (selMode == SelectMode.Window)
        //        {
        //            selected = EntityRSMgr.Instance.Window(selectBound, entity);
        //        }

        //        if (selected)
        //        {
        //            Selection selection = new Selection();
        //            selection.objectId = entity.id;
        //            sels.Add(selection);
        //        }
        //    }

        //    return sels;
        //}

        //private bool IsLineIn(IPresenter presenter, Line line, SelectMode selMode)
        //{
        //    if (selMode == SelectMode.Window)
        //    {
        //        Vector2 pnt1 = presenter.CanvasToModel(_startPoint);
        //        Vector2 pnt2 = presenter.CanvasToModel(_endPoint);
        //        LitMath.Rectangle2 selRect = new LitMath.Rectangle2(pnt1, pnt2);

        //        if (MathUtils.IsPointInRectangle(line.startPoint, selRect)
        //            && MathUtils.IsPointInRectangle(line.endPoint, selRect))
        //        {
        //            return true;
        //        }
        //        else
        //        {
        //            return false;
        //        }
        //    }
        //    else if (selMode == SelectMode.Cross)
        //    {
        //        Vector2 pnt1 = presenter.CanvasToModel(_startPoint);
        //        Vector2 pnt2 = presenter.CanvasToModel(_endPoint);

        //        Bounding selectBound = new Bounding(pnt1, pnt2);
        //        Bounding lineBound = line.bounding;
        //        if (selectBound.Contains(lineBound))
        //        {
        //            return true;
        //        }

        //        LitMath.Rectangle2 selRect = new LitMath.Rectangle2(pnt1, pnt2);

        //        LitMath.Line2 rectLine1 = new LitMath.Line2(selRect.leftBottom, selRect.leftTop);
        //        LitMath.Line2 rectLine2 = new LitMath.Line2(selRect.leftTop, selRect.rightTop);
        //        LitMath.Line2 rectLine3 = new LitMath.Line2(selRect.rightTop, selRect.rightBottom);
        //        LitMath.Line2 rectLine4 = new LitMath.Line2(selRect.rightBottom, selRect.leftBottom);
        //        LitMath.Line2 line2 = new LitMath.Line2(line.startPoint, line.endPoint);

        //        Vector2 intersection = new Vector2();
        //        if (LitMath.Line2.Intersect(rectLine1, line2, ref intersection)
        //            || LitMath.Line2.Intersect(rectLine2, line2, ref intersection)
        //            || LitMath.Line2.Intersect(rectLine3, line2, ref intersection)
        //            || LitMath.Line2.Intersect(rectLine4, line2, ref intersection))
        //        {
        //            return true;
        //        }
        //        else
        //        {
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}

        //private bool IsCircleIn(IPresenter presenter, Circle circle, SelectMode selMode)
        //{
        //    if (selMode == SelectMode.Window)
        //    {
        //        Vector2 pnt1 = presenter.CanvasToModel(_startPoint);
        //        Vector2 pnt2 = presenter.CanvasToModel(_endPoint);
        //        Bounding selectBound = new Bounding(pnt1, pnt2);
        //        Bounding circleBound = circle.bounding;

        //        if (selectBound.Contains(circleBound))
        //        {
        //            return true;
        //        }
        //        else
        //        {
        //            return false;
        //        }
        //    }
        //    else if (selMode == SelectMode.Cross)
        //    {
        //        Vector2 pnt1 = presenter.CanvasToModel(_startPoint);
        //        Vector2 pnt2 = presenter.CanvasToModel(_endPoint);
        //        Bounding selectBound = new Bounding(pnt1, pnt2);

        //        return MathUtils.BoundingCross(selectBound, circle);
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}

        //private bool IsPolylineIn(IPresenter presenter, Polyline polyline, SelectMode selMode)
        //{
        //    switch (selMode)
        //    {
        //        case SelectMode.Window:
        //            {
        //                Vector2 pnt1 = presenter.CanvasToModel(_startPoint);
        //                Vector2 pnt2 = presenter.CanvasToModel(_endPoint);
        //                Bounding selectBound = new Bounding(pnt1, pnt2);
        //                Bounding polylineBound = polyline.bounding;

        //                if (selectBound.Contains(polylineBound))
        //                {
        //                    return true;
        //                }
        //                else
        //                {
        //                    return false;
        //                }
        //            }

        //        case SelectMode.Cross:
        //            {
        //                Vector2 pnt1 = presenter.CanvasToModel(_startPoint);
        //                Vector2 pnt2 = presenter.CanvasToModel(_endPoint);
        //                Bounding selectBound = new Bounding(pnt1, pnt2);
        //                Bounding polylineBound = polyline.bounding;

        //                if (selectBound.Contains(polylineBound))
        //                {
        //                    return true;
        //                }

        //                LitMath.Rectangle2 selRect = new LitMath.Rectangle2(pnt1, pnt2);
        //                LitMath.Line2 rectLine1 = new LitMath.Line2(selRect.leftBottom, selRect.leftTop);
        //                LitMath.Line2 rectLine2 = new LitMath.Line2(selRect.leftTop, selRect.rightTop);
        //                LitMath.Line2 rectLine3 = new LitMath.Line2(selRect.rightTop, selRect.rightBottom);
        //                LitMath.Line2 rectLine4 = new LitMath.Line2(selRect.rightBottom, selRect.leftBottom);

        //                for (int i = 1; i < polyline.NumberOfVertices; ++i)
        //                {
        //                    Vector2 spnt = polyline.GetPointAt(i - 1);
        //                    Vector2 epnt = polyline.GetPointAt(i);
        //                    LitMath.Line2 line2 = new LitMath.Line2(spnt, epnt);
        //                    Vector2 intersection = new Vector2();
        //                    if (LitMath.Line2.Intersect(rectLine1, line2, ref intersection)
        //                        || LitMath.Line2.Intersect(rectLine2, line2, ref intersection)
        //                        || LitMath.Line2.Intersect(rectLine3, line2, ref intersection)
        //                        || LitMath.Line2.Intersect(rectLine4, line2, ref intersection))
        //                    {
        //                        return true;
        //                    }
        //                }

        //                return false;
        //            }

        //        default:
        //            return false;
        //    }
        //}
    }


}
