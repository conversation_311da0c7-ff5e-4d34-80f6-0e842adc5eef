﻿using System.Numerics;

namespace McLaser.EditViewerSk.Base
{

    /// <summary>
    /// 夹点类型
    /// </summary>
    public enum GripPointType
    {
        // 未定义
        Undefined = 0,
        // 端点
        End = 1,
        // 中点
        Mid = 2,
        // 中心点
        Center = 3,
        // 节点
        Node = 4,
        // 象限点
        Quad = 5,
    }


    /// <summary>
    /// 夹点
    /// </summary>
    public class GripPoint
    {
        /// <summary>
        /// 夹点类型
        /// </summary>
        public GripPointType type
        {
            get { return _type; }
            set { _type = value; }
        }
        private GripPointType _type = GripPointType.Undefined;

        /// <summary>
        /// 位置
        /// </summary>
        public Vector2 position
        {
            get { return _position; }
            set { _position = value; }
        }
        private Vector2 _position = new Vector2(0, 0);

        /// <summary>
        /// 扩展数据1
        /// </summary>
        public object xData1
        {
            get { return _xdata1; }
            set { _xdata1 = value; }
        }
        private object _xdata1 = null;

        /// <summary>
        /// 扩展数据2
        /// </summary>
        public object xData2
        {
            get { return _xdata2; }
            set { _xdata2 = value; }
        }
        private object _xdata2 = null;

        /// <summary>
        /// 构造函数
        /// </summary>
        public GripPoint(GripPointType type, Vector2 position)
        {
            _type = type;
            _position = position;
        }
    }
}