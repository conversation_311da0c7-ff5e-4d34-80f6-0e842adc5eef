//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("netDxf")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("<PERSON> © 2023")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(".net Dxf Reader-Writer")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("3.0.1")]
[assembly: System.Reflection.AssemblyProductAttribute("netDxf")]
[assembly: System.Reflection.AssemblyTitleAttribute("netDxf")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/haplokuon/netDxf")]

// Generated by the MSBuild WriteCodeFragment class.

