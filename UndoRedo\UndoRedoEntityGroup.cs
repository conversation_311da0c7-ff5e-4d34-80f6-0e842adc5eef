﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Linq;

namespace McLaser.EditViewerSk.UndoRedo
{


    internal class UndoRedoEntityGroup : UndoRedoSingle
    {
        private DocumentBase doc;
        private List<EntityBase> list;
        private EntityGroup group;
        private EntityLayer layer;
        private UndoRedoMultiple urs;
        private int index = 0;

        public override void Execute()
        {
            foreach (EntityBase entity in this.list)
                entity.IsSelected = false;
            this.group = new EntityGroup(this.list);
            group.Arg.BeforeBuffer = list.First().Arg.BeforeBuffer;
            group.Arg.AfterBuffer = list.Last().Arg.AfterBuffer;
            this.Redo();
        }

        public override void Undo() => this.urs.Undo();

        public override void Redo()
        {
            this.urs = new UndoRedoMultiple();
            index = doc.ActiveLayer.Children.IndexOf(list.First());
            this.urs.Add((IUndoRedo)new UndoRedoEntityAdd(this.doc, this.layer, new List<EntityBase>() { this.group }, index) );
            this.urs.Add((IUndoRedo)new UndoRedoEntityDelete(this.doc, this.list));
            this.urs.Execute();
        }

        public UndoRedoEntityGroup(DocumentBase doc, List<EntityBase> list, EntityLayer layer, int index=-1)
        {
            this.Name = "Entity Group";
            this.doc = doc;
            this.list = new List<EntityBase>((IEnumerable<EntityBase>)list);
            this.layer = layer;
            this.index = index;
        }
    }
}
