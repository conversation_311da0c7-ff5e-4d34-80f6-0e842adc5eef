﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Text;

namespace McLaser.EditViewerSk.Marker
{
    //激光加工参数接口
    public interface IMarkerArg : ICloneable
    {
       
        DocumentBase Document { get; set; }
      
        IMarkCommand MarkerCommand { get; set; }

        MarkTargets MarkTargets { get; set; }

        List<ViewBase> ViewTargets { get; set; }

        ILaser Laser { get; set; }

        bool IsGuided { get; set; }

        uint RepeatSelected { get; set; }

      
        float SpeedSelected { get; set; }

        bool IsSimulation { get; set; }

        double SimulationSpeed { get; set; }

        bool IsExternalStart { get; set; }

        bool IsNeedJump { get; set; }

        
        bool IsJumpToOriginAfterFinished { get; set; }

        List<Offset> Offsets { get; set; }

        IMatrixStack MatrixStack { get; set; }

        uint OffsetIndex { get; set; }

        string OffsetAndTextDataTargetEntityName { get; set; }

        DateTime StartTime { get; set; }

        DateTime EndTime { get; set; }

        double Progress { get; set; }

        bool IsSuccess { get; set; }

        bool IsVerifyScannerPowerFault { get; set; }

        bool IsMeasurementToPolt { get; set; }

        int MeasurementPlotProgram { get; set; }

        //ConcurrentStack<IPen> PenStack { get; }

        bool IsRegisteringFonts { get; set; }

        bool IsEnablePens { get; set; }

        object Tag { get; set; }

        StringBuilder BufferCmd { get; set; }

        Vector2 LaserDot { get; set; }

        int TasksIndex { get; set; }

        MarkerCoordinate2D MarkerCoord2D { get; set; }

        bool EnableMark { get; set; }

    }
}
