﻿
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Core;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Marker;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    internal class EntityGroup : EntityBase
    {
        private Vector2 location;
        private double angle;
        private List<Offset> offsets = new List<Offset>();

        private IEnumerable<EntityBase> entities;

        public IEnumerable<EntityBase> Entities
        {
            get { return entities; }
            set { entities = value; }
        }


        public new int Count => entities.Count();


 
        [Browsable(true)]
        [ReadOnly(false)]
        [Category("Data")]
        [DisplayName("Width")]
        public double Width
        {
            get => this.BoundingBox.Width;
            set
            {
                if (this.Parent != null && this.IsLocked || ((double)value <= 0.0 || this.BoundingBox.IsEmpty))
                    return;
                double radio = value / this.BoundingBox.Width;
                this.Scale(new Vector2(radio, 1f));
                this.IsNeedToRegen = true;
            }
        }

 
        [Browsable(true)]
        [ReadOnly(false)]
        [Category("Data")]
        [DisplayName("Height")]
        public double Height
        {
            get => this.BoundingBox.Height;
            set
            {
                if (this.Parent != null && this.IsLocked || ((double)value <= 0.0 || this.BoundingBox.IsEmpty))
                    return;
                double radio = value / this.BoundingBox.Height;
                this.Scale(new Vector2(1f, radio));
                this.IsNeedToRegen = true;
            }
        }


 
        [Browsable(true)]
        [ReadOnly(false)]
        [Category("Data")]
        [DisplayName("Angle")]
        public double Angle
        {
            get => this.angle;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                double angle = value - this.angle;
                if (this.Parent != null)
                    this.Rotate(angle, angle);
                this.angle = value;
            }
        }


        public EntityGroup(IEnumerable<EntityBase> list)
        {
            Name = $"Group:({list.Count()})";
            Entities = list;
            Icon =   new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/121.png"));

            //Children.AddRange(list);
        }

        public EntityGroup()
        {
            Icon =  new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/121.png"));

        }

        public override BoundingBox BoundingBox { get; set; } = BoundingBox.Empty;


        private void RegenVertextList()
        {
            foreach (EntityBase entity in this.Entities)
                entity.Regen();
        }

        private void RegenBoundRect()
        {
            this.BoundingBox.Clear();
            foreach (EntityBase entity in this.Entities)
                this.BoundingBox.Union(entity.BoundingBox);
            BoundingBox boundRect = this.BoundingBox.Clone();
            foreach (Offset offset in this.offsets)
            {
                BoundingBox br = boundRect.Clone();
                br.Transit(offset.ToVector2);
                this.BoundingBox.Union(br);
            }
            //this.location = this.BoundRect.LocationByAlign(this.align);
        }



        public override void Regen()
        {
            this.RegenVertextList();
            this.RegenBoundRect();
            this.IsNeedToRegen = false;
        }


        public override void Render(IView view)
        {
            if (view == null)
            {
                return;
            }

            if (!this.IsRenderable)
            {
                return;
            }
            if (this.IsNeedToRegen)
            {
                this.Regen();
            }
            Pen.Color = IsSelected ? SKColors.Red : SKColors.Black;

            foreach (var item in Entities)
            {
                item.IsSelected = IsSelected;   
                item.Render(view);
            }
        }

        public override bool Mark(IMarkerArg markerArg, IMarkCommand cmd)
        {
            if (!this.IsMarkerable)
                return true;
            bool flag = true;

            if (!this.IsReversable)
            {
                foreach (var item in Entities)
                {
                    flag &= item.Mark(markerArg, cmd);
                }

                if (!flag)
                    return false;
            }

            return flag;
        }



        public override bool HitTest(double x, double y, double threshold)
        {
            if (!this.BoundingBox.HitTest(x, y, threshold))
                return false;
            int num1 = -1;
            int num2 = 0;
            foreach (EntityBase entity in (IEnumerable<EntityBase>)this.Entities)
            {
                if (entity.HitTest(x, y, threshold))
                {
                    num1 = num2;
                    break;
                }
                ++num2;
            }
            return num1 >= 0;
        }

        public override bool HitTest(double left, double top, double right, double bottom, double threshold)
        {
            return this.HitTest(new BoundingBox(left, top, right, bottom), threshold);
        }

        public override bool HitTest(BoundingBox br, double threshold)
        {
            if (!this.BoundingBox.HitTest(br, threshold))
                return false;
            int num1 = -1;
            int num2 = 0;
            foreach (EntityBase entity in (IEnumerable<EntityBase>)this.Entities)
            {

                if (entity.HitTest(br, threshold))
                {
                    num1 = num2;
                    break;
                }
                ++num2;

            }
            return num1 >= 0;
        }


        public override void Translate(Vector2 translation)
        {
            throw new System.NotImplementedException();
        }


        public override object Clone()
        {
            List<EntityBase> entities = new List<EntityBase>(this.Count);
            foreach (EntityBase entity1 in Entities)
            {
                EntityBase entity2 = entity1 is ICloneable cloneable2 ? (EntityBase)cloneable2.Clone() : (EntityBase)(object)null;
                entities.Add(entity2);
            }
            EntityGroup group = new EntityGroup(entities)
            {
                Name = this.Name,
                Description = this.Description,
                Parent = this.Parent,
                IsSelected = this.IsSelected,
                IsVisible = this.IsVisible,
                IsMarkerable = this.IsMarkerable,
                IsLocked = this.IsLocked,
                IsHitTestable = this.IsHitTestable,
                Alignment = this.Alignment,
                location = this.location,
                color = this.color,
                BoundingBox = this.BoundingBox.Clone(),
                Repeats = this.Repeats,
                Width = this.Width,
                Height = this.Height,
                angle = this.angle,
                Tag = this.Tag,
                IsNeedToRegen = true,

            };
            group.offsets = new List<Offset>((IEnumerable<Offset>)this.offsets);
            return (object)group;
        }

    }
}
