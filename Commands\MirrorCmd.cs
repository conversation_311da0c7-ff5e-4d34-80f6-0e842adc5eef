﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;


namespace McLaser.EditViewerSk.Commands
{
    public class MirrorCmd : ModifyCmd
    {
        /// <summary>
        /// 源图元
        /// </summary>
        private List<EntityBase> _entities = new List<EntityBase>();

        /// <summary>
        /// 结果图元
        /// </summary>
        private List<EntityBase> _resultEntities = new List<EntityBase>();

        /// <summary>
        /// 源图元是否被删除
        /// </summary>
        private bool _isSrcDeleted = false;

        /// <summary>
        /// 镜像线
        /// </summary>
        private EntityLine _mirrorLine = null;

        /// <summary>
        /// 步骤
        /// </summary>
        private enum Step
        {
            // 选择对象
            Step1_SelectObject = 1,
            // 指定镜像线第一点
            Step2_SpecifyMirrorLinePoint1st = 2,
            // 指定镜像线第二点
            Step3_SpecifyMirrorLinePoint2nd = 3,
            // 是否删除源对象
            Step4_WhetherDelSrc = 4,
        }
        private Step _step = Step.Step1_SelectObject;

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Initialize()
        {
            base.Initialize();

            if (this._viewer.Selections.Count > 0)
            {
                foreach (EntityBase sel in this._viewer.Selections)
                {
                    //Entity entity = this.database.GetObject(sel.objectId) as Entity;
                    //if (entity != null)
                    //{
                    //    _entities.Add(entity);
                    //}
                }
            }

            if (_entities.Count > 0)
            {
                this.pointer.Mode = IndicatorMode.Locate;
                _step = Step.Step2_SpecifyMirrorLinePoint1st;
            }
            else
            {
                this._viewer.Selections.Clear();
                _step = Step.Step1_SelectObject;
                this.pointer.Mode = IndicatorMode.Select;
            }
        }

        /// <summary>
        /// 提交到数据库
        /// </summary>
        protected override void Commit()
        {
            foreach (EntityBase item in _resultEntities)
            {
                _mgr.Viewer.AppendEntity(item);
            }
        }

        /// <summary>
        /// 回滚撤销
        /// </summary>
        protected override void Rollback()
        {
            foreach (EntityBase item in _resultEntities)
            {
                //item.Erase();
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            switch (_step)
            {
                case Step.Step1_SelectObject:
                    break;

                case Step.Step2_SpecifyMirrorLinePoint1st:
                    if (e.Button == MouseButtons.Left)
                    {
                        //_mirrorLine = new Line();
                        //_mirrorLine.startPoint = this.pointer.currentSnapPoint;
                        //_mirrorLine.endPoint = _mirrorLine.startPoint;

                        //_step = Step.Step3_SpecifyMirrorLinePoint2nd;
                    }
                    break;

                case Step.Step3_SpecifyMirrorLinePoint2nd:
                    if (e.Button == MouseButtons.Left)
                    {
                        //_mirrorLine.endPoint = this.pointer.currentSnapPoint;
                        //this.UpdateResultEntities();

                        //_step = Step.Step4_WhetherDelSrc;
                        //_mgr.FinishCurrentCommand();
                    }
                    break;

                default:
                    break;
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            switch (_step)
            {
                case Step.Step1_SelectObject:
                    if (e.Button == MouseButtons.Right)
                    {
                        //if (this.presenter.selections.Count > 0)
                        //{
                        //    foreach (Selection sel in _mgr.presenter.selections)
                        //    {
                        //        DBObject dbobj = this.database.GetObject(sel.objectId);
                        //        Entity entity = dbobj as Entity;
                        //        _entities.Add(entity);
                        //    }

                        //    this.pointer.mode = Pointer.Mode.Locate;
                        //    _step = Step.Step2_SpecifyMirrorLinePoint1st;
                        //}
                        //else
                        //{
                        //    _mgr.CancelCurrentCommand();
                        //}
                    }
                    break;

                case Step.Step2_SpecifyMirrorLinePoint1st:
                    break;

                case Step.Step3_SpecifyMirrorLinePoint2nd:
                    break;

                case Step.Step4_WhetherDelSrc:
                    break;

                default:
                    break;
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (_step == Step.Step3_SpecifyMirrorLinePoint2nd)
            {
                //_mirrorLine.endPoint = this.pointer.currentSnapPoint;
                //this.UpdateResultEntities();
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                _mgr.CancelCurrentCommand();
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyUp(KeyEventArgs e)
        {
            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            //if (_step == Step.Step3_SpecifyMirrorLinePoint2nd)
            //{
            //    this.presenter.DrawEntity(g, _mirrorLine);
            //}

            //if (_step == Step.Step3_SpecifyMirrorLinePoint2nd
            //    || _step == Step.Step4_WhetherDelSrc)
            //{
            //    foreach (Entity entity in _resultEntities)
            //    {
            //        this.presenter.DrawEntity(g, entity);
            //    }
            //}
        }

        /// <summary>
        /// 刷新结果图元
        /// </summary>
        private void UpdateResultEntities()
        {
            //LitMath.Matrix3 mirrorMatrix = MathUtils.MirrorMatrix(
            //            new LitMath.Line2(_mirrorLine.tartPoint, _mirrorLine.endPoint));
            //_resultEntities.Clear();
            //foreach (Entity entity in _entities)
            //{
            //    Entity copy = entity.Clone() as Entity;
            //    copy.TransformBy(mirrorMatrix);
            //    _resultEntities.Add(copy);
            //}
        }
    }
}
