﻿using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Common
{
    //public class ObjectIdMgr
    //{
    //    //private Database _database;
    //    private uint _currentId = 10000;

    //    //public ObjectIdMgr(Database db)
    //    //{
    //    //    _database = db;
    //    //}

    //    public void reset()
    //    {
    //        if (_database != null)
    //        {
    //            ObjectId max = _database.currentMaxId;
    //            if (max.id > 10000)
    //            {
    //                _currentId = max.id;
    //            }
    //        }
    //    }

    //    public ObjectId NextId
    //    {
    //        get { return new ObjectId(++_currentId); }
    //    }
    //}
}
