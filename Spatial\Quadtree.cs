
using System.Collections.Generic;
using SkiaSharp;

namespace McLaser.EditViewerSk.Spatial
{
    public class Quadtree
    {
        private const int MaxObjects = 500000; // Increased capacity for better performance with dense data
        private const int MaxLevels = 10;  // Increased depth for large datasets

        private readonly int _level;
        private readonly SKRect _bounds;
        private readonly List<IQuadtreeObject> _objects;
        private readonly Quadtree[] _nodes;

        public Quadtree(int level, SKRect bounds)
        {
            _level = level;
            _bounds = bounds;
            _objects = new List<IQuadtreeObject>();
            _nodes = new Quadtree[4];
        }

        public void Clear()
        {
            _objects.Clear();
            for (int i = 0; i < _nodes.Length; i++)
            {
                if (_nodes[i] != null)
                {
                    _nodes[i].Clear();
                    _nodes[i] = null;
                }
            }
        }

        private void Split()
        {
            float subWidth = _bounds.Width / 2;
            float subHeight = _bounds.Height / 2;
            float x = _bounds.Left;
            float y = _bounds.Top;

            _nodes[0] = new Quadtree(_level + 1, new SKRect(x + subWidth, y, x + 2 * subWidth, y + subHeight));             // Top-right
            _nodes[1] = new Quadtree(_level + 1, new SKRect(x, y, x + subWidth, y + subHeight));                         // Top-left
            _nodes[2] = new Quadtree(_level + 1, new SKRect(x, y + subHeight, x + subWidth, y + 2 * subHeight));             // Bottom-left
            _nodes[3] = new Quadtree(_level + 1, new SKRect(x + subWidth, y + subHeight, x + 2 * subWidth, y + 2 * subHeight)); // Bottom-right
        }

        private int GetIndex(IQuadtreeObject obj)
        {
            var objBounds = obj.BoundsSK;
            if (objBounds.IsEmpty) return -1;

            int index = -1;
            float verticalMidpoint = _bounds.Left + (_bounds.Width / 2);
            float horizontalMidpoint = _bounds.Top + (_bounds.Height / 2);

            bool topQuadrant = objBounds.Bottom < horizontalMidpoint && objBounds.Top < horizontalMidpoint;
            bool bottomQuadrant = objBounds.Top > horizontalMidpoint;

            if (objBounds.Right < verticalMidpoint)
            {
                if (topQuadrant) index = 1; // Top-left
                else if (bottomQuadrant) index = 2; // Bottom-left
            }
            else if (objBounds.Left > verticalMidpoint)
            {
                if (topQuadrant) index = 0; // Top-right
                else if (bottomQuadrant) index = 3; // Bottom-right
            }
            return index;
        }

        public void Insert(IQuadtreeObject obj)
        {
            if (_nodes[0] != null)
            {
                int index = GetIndex(obj);
                if (index != -1)
                {
                    _nodes[index].Insert(obj);
                    return;
                }
            }

            _objects.Add(obj);

            if (_objects.Count > MaxObjects && _level < MaxLevels)
            {
                if (_nodes[0] == null) Split();

                // *** OPTIMIZED AND CORRECTED REDISTRIBUTION LOGIC ***
                var objectsToRedistribute = new List<IQuadtreeObject>(_objects);
                _objects.Clear();

                foreach (var item in objectsToRedistribute)
                {
                    int index = GetIndex(item);
                    if (index != -1)
                    {
                        _nodes[index].Insert(item);
                    }
                    else
                    {
                        _objects.Add(item);
                    }
                }
            }
        }

        public List<IQuadtreeObject> Query(SKRect area)
        {
            List<IQuadtreeObject> result = new List<IQuadtreeObject>();
            Query(area, result);
            return result;
        }

        public List<IQuadtreeObject> GetAllObjects()
        {
            List<IQuadtreeObject> result = new List<IQuadtreeObject>();
            GetAllObjects(result);
            return result;
        }

        private void GetAllObjects(List<IQuadtreeObject> result)
        {
            result.AddRange(_objects);
            if (_nodes[0] != null)
            {
                foreach (var node in _nodes)
                {
                    node.GetAllObjects(result);
                }
            }
        }

        private void Query(SKRect area, List<IQuadtreeObject> list)
        {
            foreach (var obj in _objects)
            {
                if (area.IntersectsWith(obj.BoundsSK))
                {
                    list.Add(obj);
                }
            }

            if (_nodes[0] != null)
            {
                foreach (var node in _nodes)
                {
                    if (node._bounds.IntersectsWith(area))
                    {
                        node.Query(area, list);
                    }
                }
            }
        }
    }
}
