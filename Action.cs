﻿using McLaser.Core;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Commands;
using McLaser.EditViewerSk.Core;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.UndoRedo;
using McLaser.Entities;
using Microsoft.Win32;
using Newtonsoft.Json;
using SkiaSharp;
using SkiaSharp.Views.Desktop;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using McLaser.EditViewerSk.Common;

namespace McLaser.EditViewerSk
{
    public class Action
    {

        internal List<EntityBase> clipboard = new List<EntityBase>();
        public DocumentBase doc;
        public static ViewBase viewer;
        private List<EntityBase> selectedEntityList;
        private Stack<IUndoRedo> undoStack;
        private Stack<IUndoRedo> redoStack;
        public int NewLayerIndex;
        private List<EntityBase> entityLaserPath;
        private bool isSimulatorTerminated;
        private Thread threadSimulator;

        internal bool UndoRedoEnable { get; set; }

        public List<EntityBase> ClipBoard
        {
            get => clipboard;
            set => clipboard = value;
        }

        public List<EntityBase> SelectedEntity
        {
            get => selectedEntityList;
            set
            {
                selectedEntityList = value;
            }
        }


        internal Action(DocumentBase owner)
        {
            this.doc = owner;

            this.selectedEntityList = new List<EntityBase>();
            this.redoStack = new Stack<IUndoRedo>();
            this.undoStack = new Stack<IUndoRedo>();
            this.UndoRedoEnable = true;
        }

        internal void Insert(IUndoRedo ur)
        {
            if (UndoRedoEnable)
                undoStack.Push(ur);
            ur.Execute();
            while (redoStack.Count > 0)
                redoStack.Pop();

            if (Config.UndoStackSize <= 0 || undoStack.Count <= Config.UndoStackSize)
                return;
            undoStack.Pop();
        }

        public void UndoRedoClear()
        {
            undoStack.Clear();
            redoStack.Clear();
            GC.Collect();
        }

        public void ActUndo()
        {
            if (undoStack.Count <= 0)
                return;
            IUndoRedo undoRedo = undoStack.Pop();
            redoStack.Push(undoRedo);
            undoRedo.Undo();
            SelectedEntity = null;
            viewer?.RepaintCanvas();
        }

        public void ActRedo()
        {
            if (redoStack.Count <= 0)
                return;
            IUndoRedo undoRedo = redoStack.Pop();
            undoStack.Push(undoRedo);
            undoRedo.Redo();
            SelectedEntity = null;
            viewer?.RepaintCanvas();
        }

        public bool ActSave(DocumentBase doc, string fileName)
        {
            JsonSerializerSettings settings = new JsonSerializerSettings()
            {
                TypeNameHandling = TypeNameHandling.All,
                PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                TypeNameAssemblyFormatHandling = TypeNameAssemblyFormatHandling.Full
            };
            TextWriter textWriter = (TextWriter)null;
            doc.FileName = fileName;
            try
            {
                string str = JsonConvert.SerializeObject((object)doc, Formatting.Indented, settings);
                textWriter = (TextWriter)new StreamWriter(fileName, false);
                textWriter.Write(str);
            }
            finally
            {
                textWriter?.Close();
            }
            AssociateFileType();
            return true;
        }

        private void AssociateFileType()
        {
            string fileExtension = ".mc";
            string progId = "MyApp.mc";
            string description = "铭创激光加工文件";
            string iconPath = AppDomain.CurrentDomain.BaseDirectory + "//MyResources//MCT64x64.ico";
            string appPath = AppDomain.CurrentDomain.BaseDirectory + @"MCLaser.exe";

            Registry.SetValue(@"HKEY_CLASSES_ROOT\" + fileExtension, "", progId);
            using (RegistryKey progIdKey = Registry.CurrentUser.CreateSubKey(@"Software\Classes\" + progId))
            {
                progIdKey?.SetValue("", description);

                using (RegistryKey iconKey = progIdKey.CreateSubKey("DefaultIcon"))
                {
                    iconKey?.SetValue("", iconPath);
                }

                using (RegistryKey shellKey = progIdKey.CreateSubKey(@"shell\open\command"))
                {
                    shellKey?.SetValue("", $"\"{appPath}\" \"%1\"");
                }
            }
        }
        public static DocumentBase ActLoad(string fileName)
        {
            JsonSerializerSettings settings = new JsonSerializerSettings()
            {
                TypeNameHandling = TypeNameHandling.All,
                PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                TypeNameAssemblyFormatHandling = TypeNameAssemblyFormatHandling.Full,
                MissingMemberHandling = MissingMemberHandling.Ignore
            };

            TextReader textReader = (TextReader)null;
            try
            {
                textReader = (TextReader)new StreamReader(fileName);
                DocumentBase document = JsonConvert.DeserializeObject<DocumentBase>(textReader.ReadToEnd(), settings);
                if (document == null)
                    return (DocumentBase)null;
                foreach (EntityLayer layer in document.Layers)
                {
                    foreach (EntityBase entity in layer.Children)
                        entity.Parent = (EntityBase)layer;
                }
                if (document.Layers.Count > 0)
                {
                    document.ActiveLayer = document.Layers[0];
                    document.ActiveLayer.IsNeedToRegen = true;
                }
                viewer?.ClearRenderer();
                foreach (var layer in document.Layers)
                {
                    viewer?.AddEntitiesToRenderer(layer.Children);
                }
                return document;
            }
            catch (Exception ex)
            {

            }
            finally
            {
                textReader?.Close();
            }
            return (DocumentBase)null;


        }

        public bool ActLayerNew()
        {
            //doc = new DocumentBase();
            doc.Layers.Clear();
            EntityLayer layer = new EntityLayer();
            layer.Name = $"Layer{doc.Layers.Count}";
            doc.Layers.Add(layer);
            doc.ActiveLayer = layer;
            UndoRedoClear();
            doc.SelectedEntitys = new List<EntityBase>();
            doc.View.RepaintCanvas();
            return true;
        }


        public bool ActDocNew()
        {
            DocumentBase doc = new DocumentBase();
            EntityLayer layer = new EntityLayer();
            layer.Name = $"Layer{this.doc.Layers.Count}";
            this.doc.Layers.Add(layer);
            this.doc.ActiveLayer = layer;
            UndoRedoClear();
            this.doc.SelectedEntitys = new List<EntityBase>();
            doc.View = viewer;
            viewer.Document = doc;

            this.doc.View.RepaintCanvas();
            return true;
        }


        public async void ActReadDxf()
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Title = "选择DXF文件",
                    Filter = "DXF 文件 (*.dxf)|*.dxf|所有文件 (*.*)|*.*",
                    FilterIndex = 1,
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    string filePath = openFileDialog.FileName;
                    var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                    System.Diagnostics.Debug.WriteLine($"开始导入DXF文件: {filePath}");

                    // TODO: Show a progress bar to the user
                    var importResult = await DxfImporter.ImportAsync(filePath, progress =>
                    {
                        System.Diagnostics.Debug.WriteLine($"导入进度: {progress:F1}%");
                        // Update progress bar here, e.g., progressBar.Value = progress;
                    });

                    if (importResult == null)
                    {
                        MessageBox.Show("DXF文件导入失败，可能文件损坏或格式不支持。", "导入错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    if (importResult.Entities.Count == 0)
                    {
                        MessageBox.Show("DXF文件中没有找到可导入的图元。", "导入警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine($"成功导入 {importResult.Entities.Count} 个图元");
                    System.Diagnostics.Debug.WriteLine($"边界框: {importResult.BoundingBox}");

                    // 清理现有内容
                    doc.ActiveLayer.Children.Clear();
                    viewer?.ClearRenderer();

                    // 关键修复：使用DXF的实际边界重新创建渲染器
                    var inflatedBounds = importResult.BoundingBox;
                    if (!inflatedBounds.IsEmpty)
                    {
                        inflatedBounds.Inflate(Math.Max(inflatedBounds.Width * 0.1f, 100),
                                             Math.Max(inflatedBounds.Height * 0.1f, 100));
                    }

                    System.Diagnostics.Debug.WriteLine($"重置渲染器边界: {inflatedBounds}");
                    viewer?.ResetRenderer(inflatedBounds);

                    // 将实体添加到文档层
                    foreach (var entity in importResult.Entities)
                    {
                        entity.Parent = doc.ActiveLayer;
                        doc.ActiveLayer.Children.Add(entity);
                    }

                    System.Diagnostics.Debug.WriteLine("开始批量添加图元到渲染器...");

                    // 使用优化的批量添加方法
                    viewer?.AddEntitiesToRendererBatch(doc.ActiveLayer.Children, false);

                    // 最后，缩放视图以适应内容
                    var bounds = new BoundingBox(importResult.BoundingBox.Left, importResult.BoundingBox.Top,
                                               importResult.BoundingBox.Right, importResult.BoundingBox.Bottom);
                    viewer?.OnZoomFit(bounds);

                    stopwatch.Stop();
                    System.Diagnostics.Debug.WriteLine($"DXF导入完成，总耗时: {stopwatch.ElapsedMilliseconds}ms");

                    MessageBox.Show($"成功导入 {importResult.Entities.Count} 个图元\n耗时: {stopwatch.ElapsedMilliseconds}ms",
                                  "导入成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (OutOfMemoryException ex)
            {
                System.Diagnostics.Debug.WriteLine($"内存不足错误: {ex.Message}");
                MessageBox.Show("文件过大，内存不足。请尝试关闭其他程序后重试。", "内存错误", MessageBoxButton.OK, MessageBoxImage.Error);
                GC.Collect();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导入错误: {ex}");
                MessageBox.Show($"导入过程中发生错误:\n{ex.Message}\n\n详细信息已记录到调试输出。", "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private EntityArc CreateArc(Arc e)
        {
            EntityArc arc = new EntityArc();
            McLaser.Vector3 center = e.Center;
            double x = center.X;
            center = e.Center;
            double y = center.Y;
            arc.Center = new Vector2((float)x, (float)y);
            arc.StartAngle = (float)e.StartAngle;
            arc.SweepAngle = e.EndAngle > e.StartAngle ? (float)(e.EndAngle - e.StartAngle) : (float)(360.0 + e.EndAngle - e.StartAngle);
            arc.Radius = (float)e.Radius;

            var list = e.PolygonalVertexes(30);
            for (int i = 0; i < list.Count; i++)
            {
                arc.Vertices.Add(new Vector2((float)list[i].X, (float)list[i].Y));
            }
            //arc.Regen();
            return arc;
        }

        private EntityLine CreateLine(Line e)
        {
            EntityLine line = new EntityLine();
            line.StartPoint = new Vector2((float)e.StartPoint.X, (float)e.StartPoint.Y);
            line.EndPoint = new Vector2((float)e.EndPoint.X, (float)e.EndPoint.Y);
            //arc.Regen();
            return line;
        }

        private EntityCircle CreateCircle(Circle e)
        {
            EntityCircle circle = new EntityCircle();
            circle.Center = new Vector2(e.Center.X, e.Center.Y);
            circle.Radius = e.Radius;
            
            return circle;
        }

        private EntityPolyline2D CreatePolyline(Polyline2D e)
        {
         
            EntityPolyline2D polyline = new EntityPolyline2D();
            polyline.IsClosed = e.IsClosed;
            foreach (var vertex in e.Vertexes)
            {
                polyline.Points.Add(new Vector2(vertex.Position.X, vertex.Position.Y));
                polyline.Bulges.Add(vertex.Bulge);
            }

            for (int i = 0; i < polyline.Points.Count - 1; i++)
            {
                var start = polyline.Points[i];
                var end = polyline.Points[i + 1];
                var bulge = polyline.Bulges[i];

                if (bulge == 0)
                {
                    EntityLine line = new EntityLine(start.X, start.Y, end.X, end.Y);
                    polyline.Entities.Add(line);
                }


                // 计算中点和弧的几何信息
                double dx = end.X - start.X;
                double dy = end.Y - start.Y;
                double chordLength = Math.Sqrt(dx * dx + dy * dy);

                // 中心角 θ = 4 * atan(bulge)
                double theta = 4 * Math.Atan(bulge);

                // 弧半径 R = chordLength / (2 * sin(θ / 2))
                double radius = chordLength / (2 * Math.Abs((float)Math.Sin(theta / 2)));

                // 计算弧的中点
                double midX = (start.X + end.X) / 2;
                double midY = (start.Y + end.Y) / 2;

                // 计算弧心（根据方向确定弧心位置）
                double direction = bulge > 0 ? -1 : 1;
                double centerX = midX - direction * (dy / chordLength) * radius * (float)Math.Cos(theta / 2);
                double centerY = midY + direction * (dx / chordLength) * radius * (float)Math.Cos(theta / 2);

                // 弧心
                Vector2 center = new Vector2(centerX, centerY);

                // 计算起始角度和终止角度
                double startAngle = (float)Math.Atan2(start.Y - center.Y, start.X - center.X);
                double endAngle = (float)Math.Atan2(end.Y - center.Y, end.X - center.X);

                // 将角度转换为度
                startAngle = startAngle * 180 / (float)Math.PI;
                endAngle = endAngle * 180 / (float)Math.PI;

                // 确保角度范围正确
                if (direction > 0 && endAngle < startAngle)
                {
                    endAngle += 360;
                }
                else if (direction < 0 && startAngle < endAngle)
                {
                    startAngle += 360;
                }

                EntityArc arc = new EntityArc(centerX, centerY, radius, startAngle, endAngle - startAngle);
                polyline.Entities.Add(arc);
            }
            return polyline;
        }

        private EntitySpline CreateSpline(Spline e)
        {
            List<Vector3> vector3List = e.PolygonalVertexes(e.ControlPoints.Count() * 3);
            EntitySpline spline = new EntitySpline()
            {
                Color = e.Color.IsByLayer ? Config.ViewDefaultEntityColor : AciColor.FromCadIndex(e.Color.Index).ToColor().ToSKColor(),
                IsClosed = e.IsClosed
            };
            foreach (Vector3 vector3 in vector3List)
                spline.Children.Add(new LwPolyLineVertex(vector3.X, vector3.Y));
            return spline;
        }

        public bool ActEntityAdd(EntityBase entity, EntityLayer layer = null)
        {
            if (entity == null)
                return false;
            Insert(new UndoRedoEntityAdd(doc, layer, new List<EntityBase>() { entity }));
            viewer?.AddEntityToRenderer(entity);
            return true;
        }

        public bool ActEntityDelete(List<EntityBase> entities)
        {
            if (entities == null || entities.Count == 0)
                return false;
            Insert(new UndoRedoEntityDelete(doc, entities));
            viewer?.RemoveEntitiesFromRenderer(entities);
            return true;
        }

        public bool ActEntityGroup(List<EntityBase> entities, EntityLayer layer = null)
        {
            if (entities == null) return false;

            var entLayer = entities.Where(x => x.GetType() == typeof(EntityLayer)).ToList();
            if (entLayer != null && entLayer.Count() > 0)
            {
                for (int i = 0; i < entLayer.Count(); i++)
                {
                    entities.Remove(entLayer[i]);
                }
            }

            if (entities == null || entities.Count < 1 || entities.Count(x => x.GetType() == typeof(EntityLayer)) > 0)
                return false;
            if (entities.Count == 1 && entities[0] is EntityGroup)
                return false;

            if (layer == null) layer = doc.ActiveLayer;

            //有问题
            Insert((IUndoRedo)new UndoRedoEntityGroup(doc, entities, layer));

            //EntityGroup group = new EntityGroup(entities);
            //group.IsNeedToRegen = true;
            //group.Arg.BeforeBuffer = entities.First().Arg.BeforeBuffer;
            //group.Arg.AfterBuffer = entities.Last().Arg.BeforeBuffer;
            //int index = layer.Children.IndexOf(entities.First());
            //layer.Insert(index, group);
            //foreach (var item in entities)
            //{
            //    layer.Remove(item);
            //}

            viewer?.RepaintCanvas();
            return true;
        }

        public bool ActEntityUnGroup(List<EntityBase> entities, EntityLayer layer = null)
        {
            if (entities == null || entities.Count == 0)
                return false;

            if (layer == null) layer = doc.ActiveLayer;
            //有问题
            Insert((IUndoRedo)new UndoRedoEntityUnGroup(doc, entities, layer));

            //foreach (var item in entities)
            //{
            //    if (item is EntityGroup)
            //    {
            //        int index = layer.Children.IndexOf(item);
            //        foreach (var entity in ((EntityGroup)item).Entities)
            //        {
            //            entity.IsSelected = true;
            //            entity.IsNeedToRegen = true;
            //            if (entity == ((EntityGroup)item).Entities.First())
            //            {
            //                entity.Arg.BeforeBuffer = item.Arg.BeforeBuffer;
            //            }
            //            if (entity == ((EntityGroup)item).Entities.Last())
            //            {
            //                entity.Arg.AfterBuffer = item.Arg.AfterBuffer;
            //            }
            //            layer.Insert(index++, entity);
            //        }
            //        layer.Remove(item);
            //    }
            //}

            viewer?.RepaintCanvas();
            return true;
        }

        public bool ActEntitySort(List<EntityBase> entities, EntityLayer layer, EntitySort sort)
        {
            if (layer == null || entities == null || entities.Count == 0)
                return false;
            Insert((IUndoRedo)new UndoRedoEntitySort(doc, layer, entities, sort));
            return true;
        }

        public bool ActEntityReverse(List<EntityBase> entities, EntityLayer layer)
        {
            if (layer == null || entities == null || entities.Count == 0)
                return false;
            Insert((IUndoRedo)new UndoRedoEntityReverse(doc, layer, entities));
            return true;
        }

        public bool ActDrawLine()
        {
            LineCmd cmd = new LineCmd(this.doc);
            viewer?.OnCommand(cmd);
            return true;
        }

        public bool ActDrawPoint()
        {
            PointCmd cmd = new PointCmd(this.doc);
            viewer?.OnCommand(cmd);
            return true;
        }

        public bool ActDrawRectangle()
        {
            RectangleCmd cmd = new RectangleCmd(this.doc);
            viewer?.OnCommand(cmd);
            return true;
        }

        public bool ActDrawCircle()
        {
            CircleCmd cmd = new CircleCmd(this.doc);
            viewer?.OnCommand(cmd);
            return true;
        }

        public bool ActDrawPolyline()
        {
            PolylineCmd cmd = new PolylineCmd(this.doc);
            viewer?.OnCommand(cmd);
            return true;
        }

        public bool ActDrawArc()
        {
            ArcCmd cmd = new ArcCmd(this.doc);
            viewer?.OnCommand(cmd);
            return true;
        }


        public event EventHandler EventMarkingViewRegisted;
        public void RegisterMarkView(object viewer)
        {
            EventMarkingViewRegisted?.Invoke(viewer, null);
        }

        public static event EventHandler EventEditorViewPublished;
        public static void PublishEditorView(object viewer)
        {
            EventEditorViewPublished?.Invoke(viewer, null);
        }


        public static event EventHandler EventBufferPublished;
        public void PublishBuffer(object viewer)
        {
            EventBufferPublished?.Invoke(viewer, null);
        }

    }
}
