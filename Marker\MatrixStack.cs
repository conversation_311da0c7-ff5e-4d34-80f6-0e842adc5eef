﻿using System;
using System.Collections.Generic;

namespace McLaser.EditViewerSk.Marker
{
    public sealed class MatrixStack : IMatrixStack, ICloneable, IEquatable<MatrixStack>
    {
        private Stack<Matrix3> stack;
        private Matrix3 matrixResult;
        private bool isModified;

        public Matrix3 ToResult
        {
            get
            {
                if (!this.isModified)
                    return this.matrixResult;
                this.matrixResult =  Matrix3.Identity;
                foreach (Matrix3 Matrix3 in this.stack)
                    this.matrixResult *= Matrix3;
                return this.matrixResult;
            }
        }

        public int Count => this.stack.Count;

        public MatrixStack()
        {
            this.stack = new Stack<Matrix3>();
            this.stack.Push(Matrix3.Identity);
            this.isModified = true;
        }

        public MatrixStack(MatrixStack matrixStack)
        {
            this.stack = matrixStack.stack;
            this.isModified = true;
        }

        public object Clone() => (object)new MatrixStack(this);

        public bool Equals(MatrixStack other) => other != null && MathHelper.IsEqual(other.ToResult.M11, this.ToResult.M11) && (MathHelper.IsEqual(other.ToResult.M12, this.ToResult.M12) && MathHelper.IsEqual(other.ToResult.M21, this.ToResult.M21)) && (MathHelper.IsEqual(other.ToResult.M22, this.ToResult.M22) && MathHelper.IsEqual(other.ToResult.M31, this.ToResult.M31)) && MathHelper.IsEqual(other.ToResult.M32, this.ToResult.M32);

        public void Clear()
        {
            this.stack.Clear();
            this.stack.Push(Matrix3.Identity);
            this.isModified = true;
        }

        public void Push(Matrix3 m)
        {
            this.stack.Push(m);
            this.isModified = true;
        }

        public void Pop(out Matrix3 matrix)
        {
            matrix = this.stack.Pop();
            this.isModified = true;
        }

        public void Pop()
        {
            this.stack.Pop();
            this.isModified = true;
        }

        public void Push(double angle)
        {
            this.stack.Push(Matrix3.RotationZ((angle * Math.PI / 180.0)));
            this.isModified = true;
        }

        public void Push(double dx, double dy)
        {
            this.stack.Push(Matrix3.Translation(dx, dy));
            this.isModified = true;
        }

        public void Push(Vector2 translate)
        {
            this.stack.Push(Matrix3.Translation(translate));
            this.isModified = true;
        }

        public void Push(double dx, double dy, double angle)
        {
            this.Push(Matrix3.Translation(dx, dy) * Matrix3.RotationZ((angle * Math.PI / 180.0)));
        }
    }
}
