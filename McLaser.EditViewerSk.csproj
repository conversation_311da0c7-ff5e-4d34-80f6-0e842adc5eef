﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3C882003-3D24-47CD-9E33-25512861C817}</ProjectGuid>
    <OutputType>library</OutputType>
    <RootNamespace>McLaser.EditViewerSk</RootNamespace>
    <AssemblyName>McLaser.EditViewerSk</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <LangVersion>10</LangVersion>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="McLaser.Core">
      <HintPath>..\bin\McLaser.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xaml.Behaviors">
      <HintPath>..\..\packages\Microsoft.Xaml.Behaviors.Wpf.1.1.135\lib\net462\Microsoft.Xaml.Behaviors.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="OpenTK, Version=3.1.0.0, Culture=neutral, PublicKeyToken=bad199fe84eb3df4, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OpenTK.3.1.0\lib\net20\OpenTK.dll</HintPath>
    </Reference>
    <Reference Include="OpenTK.GLControl, Version=3.1.0.0, Culture=neutral, PublicKeyToken=bad199fe84eb3df4, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OpenTK.GLControl.3.1.0\lib\net20\OpenTK.GLControl.dll</HintPath>
    </Reference>
    <Reference Include="PropertyTools, Version=3.1.0.0, Culture=neutral, PublicKeyToken=ea0c9f2b460934d0, processorArchitecture=MSIL">
      <HintPath>..\packages\PropertyTools.3.1.0\lib\net45\PropertyTools.dll</HintPath>
    </Reference>
    <Reference Include="PropertyTools.Wpf, Version=3.1.0.0, Culture=neutral, PublicKeyToken=ea0c9f2b460934d0, processorArchitecture=MSIL">
      <HintPath>..\packages\PropertyTools.Wpf.3.1.0\lib\net45\PropertyTools.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="SkiaSharp, Version=3.116.0.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SkiaSharp.3.116.1\lib\net462\SkiaSharp.dll</HintPath>
    </Reference>
    <Reference Include="SkiaSharp.Views.Desktop.Common, Version=3.116.0.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SkiaSharp.Views.Desktop.Common.3.116.1\lib\net462\SkiaSharp.Views.Desktop.Common.dll</HintPath>
    </Reference>
    <Reference Include="SkiaSharp.Views.WindowsForms, Version=3.116.0.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SkiaSharp.Views.WindowsForms.3.116.1\lib\net462\SkiaSharp.Views.WindowsForms.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Common, Version=4.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Drawing.Common.4.7.3\lib\net461\System.Drawing.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\Command.cs" />
    <Compile Include="Base\Config.cs" />
    <Compile Include="Base\CSYS.cs" />
    <Compile Include="Base\IconCache.cs" />
    <Compile Include="Behaviors\BindableTreeViewSelectedItemBehavior.cs" />
    <Compile Include="Convertors\BooleanTypeDescriptionProvider.cs" />
    <Compile Include="Convertors\BooleanTypeDescriptor.cs" />
    <Compile Include="Convertors\CheckBoxEditor.cs" />
    <Compile Include="Convertors\LongTextEditor.cs" />
    <Compile Include="Convertors\PropertyGridWrapper.cs" />
    <Compile Include="Core\CachedSkiaRenderer.cs" />
    <Compile Include="Core\IRenderer.cs" />
    <Compile Include="Spatial\IQuadtreeObject.cs" />
    <Compile Include="Spatial\Quadtree.cs" />
    <Compile Include="Entitys\EntityBase.cs" />
    <Compile Include="Base\GripPoint.cs" />
    <Compile Include="Base\LineType.cs" />
    <Compile Include="Base\LineWeight.cs" />
    <Compile Include="Base\LocateCross.cs" />
    <Compile Include="Base\ObjectId.cs" />
    <Compile Include="Base\ObjectSnapMode.cs" />
    <Compile Include="Base\ObjectSnapPoint.cs" />
    <Compile Include="Base\PickupBox.cs" />
    <Compile Include="Commands\ArcCmd.cs" />
    <Compile Include="Commands\CircleCmd.cs" />
    <Compile Include="Commands\LineCmd.cs" />
    <Compile Include="Commands\PointCmd.cs" />
    <Compile Include="Commands\PolylineCmd.cs" />
    <Compile Include="Commands\RectangleCmd.cs" />
    <Compile Include="Common\AsyncCommand.cs" />
    <Compile Include="Common\MgrIndicator.cs" />
    <Compile Include="Base\SelectRectangle.cs" />
    <Compile Include="Base\TableIds.cs" />
    <Compile Include="Commands\DeleteCmd.cs" />
    <Compile Include="Commands\DrawCmd.cs" />
    <Compile Include="Commands\EditCmd.cs" />
    <Compile Include="Commands\GripPointMoveCmd.cs" />
    <Compile Include="Commands\LinesChainCmd.cs" />
    <Compile Include="Commands\MirrorCmd.cs" />
    <Compile Include="Commands\ModifyCmd.cs" />
    <Compile Include="Commands\MoveCmd.cs" />
    <Compile Include="Commands\OffsetCmd.cs" />
    <Compile Include="Common\CommandsFactory.cs" />
    <Compile Include="Common\CommandsMgr.cs" />
    <Compile Include="Base\DocumentBase.cs" />
    <Compile Include="Base\ViewBase.cs" />
    <Compile Include="Commands\RedoCmd.cs" />
    <Compile Include="Commands\UndoCmd.cs" />
    <Compile Include="Common\AnchorsMgr.cs" />
    <Compile Include="Common\GDIResMgr.cs" />
    <Compile Include="Common\MathUtils.cs" />
    <Compile Include="Common\ObjectIdMgr.cs" />
    <Compile Include="Common\MgrSnap.cs" />
    <Compile Include="Common\ObservableObject.cs" />
    <Compile Include="Common\RelayCommand.cs" />
    <Compile Include="Common\XmlFiler.cs" />
    <Compile Include="Common\DxfImporter.cs" />
    <Compile Include="Dxf\AciColor.cs" />
    <Compile Include="Dxf\BezierCurve.cs" />
    <Compile Include="Dxf\BezierCurveCubic.cs" />
    <Compile Include="Dxf\BezierCurveQuadratic.cs" />
    <Compile Include="Dxf\Blocks\Block.cs" />
    <Compile Include="Dxf\Blocks\BlockAttributeDefinitionChangeEventArgs.cs" />
    <Compile Include="Dxf\Blocks\BlockEntityChangeEventArgs.cs" />
    <Compile Include="Dxf\Blocks\BlockRecord.cs" />
    <Compile Include="Dxf\Blocks\BlockTypeFlags.cs" />
    <Compile Include="Dxf\Blocks\EndBlock.cs" />
    <Compile Include="Dxf\BoundingRectangle.cs" />
    <Compile Include="Dxf\ClippingBoundary.cs" />
    <Compile Include="Dxf\ClippingBoundaryType.cs" />
    <Compile Include="Dxf\Collections\ApplicationRegistries.cs" />
    <Compile Include="Dxf\Collections\AttributeCollection.cs" />
    <Compile Include="Dxf\Collections\AttributeDefinitionDictionary.cs" />
    <Compile Include="Dxf\Collections\AttributeDefinitionDictionaryEventArgs.cs" />
    <Compile Include="Dxf\Collections\BlockRecords.cs" />
    <Compile Include="Dxf\Collections\DimensionStyleOverrideDictionary.cs" />
    <Compile Include="Dxf\Collections\DimensionStyleOverrideDictionaryEventArgs.cs" />
    <Compile Include="Dxf\Collections\DimensionStyles.cs" />
    <Compile Include="Dxf\Collections\DrawingEntities.cs" />
    <Compile Include="Dxf\Collections\DxfObjectReferences.cs" />
    <Compile Include="Dxf\Collections\EntityCollection.cs" />
    <Compile Include="Dxf\Collections\EntityCollectionEventArgs.cs" />
    <Compile Include="Dxf\Collections\Groups.cs" />
    <Compile Include="Dxf\Collections\ImageDefinitions.cs" />
    <Compile Include="Dxf\Collections\Layers.cs" />
    <Compile Include="Dxf\Collections\LayerStateManager.cs" />
    <Compile Include="Dxf\Collections\Layouts.cs" />
    <Compile Include="Dxf\Collections\Linetypes.cs" />
    <Compile Include="Dxf\Collections\MLineStyles.cs" />
    <Compile Include="Dxf\Collections\ObservableCollection.cs" />
    <Compile Include="Dxf\Collections\ObservableCollectionEventArgs.cs" />
    <Compile Include="Dxf\Collections\ObservableDictionary.cs" />
    <Compile Include="Dxf\Collections\ObservableDictionaryEventArgs.cs" />
    <Compile Include="Dxf\Collections\ShapeStyles.cs" />
    <Compile Include="Dxf\Collections\SupportFolders.cs" />
    <Compile Include="Dxf\Collections\TableObjects.cs" />
    <Compile Include="Dxf\Collections\TextStyles.cs" />
    <Compile Include="Dxf\Collections\UCSs.cs" />
    <Compile Include="Dxf\Collections\UnderlayDgnDefinitions.cs" />
    <Compile Include="Dxf\Collections\UnderlayDwfDefinitions.cs" />
    <Compile Include="Dxf\Collections\UnderlayPdfDefinitions.cs" />
    <Compile Include="Dxf\Collections\Views.cs" />
    <Compile Include="Dxf\Collections\VPorts.cs" />
    <Compile Include="Dxf\Collections\XDataDictionary.cs" />
    <Compile Include="Dxf\CoordinateSystem.cs" />
    <Compile Include="Dxf\DxfDocument.cs" />
    <Compile Include="Dxf\DxfObject.cs" />
    <Compile Include="Dxf\DxfObjectCode.cs" />
    <Compile Include="Dxf\DxfObjectReference.cs" />
    <Compile Include="Dxf\Entities\AlignedDimension.cs" />
    <Compile Include="Dxf\Entities\Angular2LineDimension.cs" />
    <Compile Include="Dxf\Entities\Angular3PointDimension.cs" />
    <Compile Include="Dxf\Entities\Arc.cs" />
    <Compile Include="Dxf\Entities\ArcLengthDimension.cs" />
    <Compile Include="Dxf\Entities\Attribute.cs" />
    <Compile Include="Dxf\Entities\AttributeChangeEventArgs.cs" />
    <Compile Include="Dxf\Entities\AttributeDefinition.cs" />
    <Compile Include="Dxf\Entities\AttributeFlags.cs" />
    <Compile Include="Dxf\Entities\Circle.cs" />
    <Compile Include="Dxf\Entities\DatumReferenceValue.cs" />
    <Compile Include="Dxf\Entities\DiametricDimension.cs" />
    <Compile Include="Dxf\Entities\Dimension.cs" />
    <Compile Include="Dxf\Entities\DimensionArrowhead.cs" />
    <Compile Include="Dxf\Entities\DimensionBlock.cs" />
    <Compile Include="Dxf\Entities\DimensionType.cs" />
    <Compile Include="Dxf\Entities\DimensionTypeFlags.cs" />
    <Compile Include="Dxf\Entities\Ellipse.cs" />
    <Compile Include="Dxf\Entities\EndSequence.cs" />
    <Compile Include="Dxf\Entities\EntityChangeEventArgs.cs" />
    <Compile Include="Dxf\Entities\EntityObject.cs" />
    <Compile Include="Dxf\Entities\EntityType.cs" />
    <Compile Include="Dxf\Entities\Face3D.cs" />
    <Compile Include="Dxf\Entities\Face3DEdgeFlags.cs" />
    <Compile Include="Dxf\Entities\Hatch.cs" />
    <Compile Include="Dxf\Entities\HatchBoundaryPath.cs" />
    <Compile Include="Dxf\Entities\HatchBoundaryPathTypeFlags.cs" />
    <Compile Include="Dxf\Entities\HatchFillType.cs" />
    <Compile Include="Dxf\Entities\HatchGradientPattern.cs" />
    <Compile Include="Dxf\Entities\HatchGradientPatternType.cs" />
    <Compile Include="Dxf\Entities\HatchPattern.cs" />
    <Compile Include="Dxf\Entities\HatchPatternLineDefinition.cs" />
    <Compile Include="Dxf\Entities\HatchStyle.cs" />
    <Compile Include="Dxf\Entities\HatchType.cs" />
    <Compile Include="Dxf\Entities\Image.cs" />
    <Compile Include="Dxf\Entities\ImageDisplayFlags.cs" />
    <Compile Include="Dxf\Entities\Insert.cs" />
    <Compile Include="Dxf\Entities\Leader.cs" />
    <Compile Include="Dxf\Entities\LeaderPathType.cs" />
    <Compile Include="Dxf\Entities\Line.cs" />
    <Compile Include="Dxf\Entities\LinearDimension.cs" />
    <Compile Include="Dxf\Entities\Mesh.cs" />
    <Compile Include="Dxf\Entities\MeshEdge.cs" />
    <Compile Include="Dxf\Entities\MLine.cs" />
    <Compile Include="Dxf\Entities\MLineFlags.cs" />
    <Compile Include="Dxf\Entities\MLineJustification.cs" />
    <Compile Include="Dxf\Entities\MLineVertex.cs" />
    <Compile Include="Dxf\Entities\MText.cs" />
    <Compile Include="Dxf\Entities\MTextAttachmentPoint.cs" />
    <Compile Include="Dxf\Entities\MTextDrawingDirection.cs" />
    <Compile Include="Dxf\Entities\MTextFormattingOptions.cs" />
    <Compile Include="Dxf\Entities\MTextLineSpacingStyle.cs" />
    <Compile Include="Dxf\Entities\MTextParagraphAlignment.cs" />
    <Compile Include="Dxf\Entities\MTextParagraphOptions.cs" />
    <Compile Include="Dxf\Entities\MTextParagraphVerticalAlignment.cs" />
    <Compile Include="Dxf\Entities\OrdinateDimension.cs" />
    <Compile Include="Dxf\Entities\OrdinateDimensionAxis.cs" />
    <Compile Include="Dxf\Entities\Point.cs" />
    <Compile Include="Dxf\Entities\PolyfaceMesh.cs" />
    <Compile Include="Dxf\Entities\PolyfaceMeshFace.cs" />
    <Compile Include="Dxf\Entities\PolygonMesh.cs" />
    <Compile Include="Dxf\Entities\Polyline.cs" />
    <Compile Include="Dxf\Entities\Polyline2D.cs" />
    <Compile Include="Dxf\Entities\Polyline2DVertex.cs" />
    <Compile Include="Dxf\Entities\Polyline3D.cs" />
    <Compile Include="Dxf\Entities\PolylineSmoothType.cs" />
    <Compile Include="Dxf\Entities\PolylineTypeFlags.cs" />
    <Compile Include="Dxf\Entities\RadialDimension.cs" />
    <Compile Include="Dxf\Entities\Ray.cs" />
    <Compile Include="Dxf\Entities\Shape.cs" />
    <Compile Include="Dxf\Entities\Solid.cs" />
    <Compile Include="Dxf\Entities\Spline.cs" />
    <Compile Include="Dxf\Entities\SplineCreationMethod.cs" />
    <Compile Include="Dxf\Entities\SplineKnotParameterization.cs" />
    <Compile Include="Dxf\Entities\SplineTypeFlags.cs" />
    <Compile Include="Dxf\Entities\Text.cs" />
    <Compile Include="Dxf\Entities\TextAligment.cs" />
    <Compile Include="Dxf\Entities\Tolerance.cs" />
    <Compile Include="Dxf\Entities\ToleranceEntry.cs" />
    <Compile Include="Dxf\Entities\ToleranceGeometricSymbol.cs" />
    <Compile Include="Dxf\Entities\ToleranceMaterialCondition.cs" />
    <Compile Include="Dxf\Entities\ToleranceValue.cs" />
    <Compile Include="Dxf\Entities\Trace.cs" />
    <Compile Include="Dxf\Entities\Underlay.cs" />
    <Compile Include="Dxf\Entities\UnderlayDisplayFlags.cs" />
    <Compile Include="Dxf\Entities\Vertex.cs" />
    <Compile Include="Dxf\Entities\VertexTypeFlags.cs" />
    <Compile Include="Dxf\Entities\Viewport.cs" />
    <Compile Include="Dxf\Entities\ViewportStatusFlags.cs" />
    <Compile Include="Dxf\Entities\Wipeout.cs" />
    <Compile Include="Dxf\Entities\XLine.cs" />
    <Compile Include="Dxf\GTE\BandedMatrix.cs" />
    <Compile Include="Dxf\GTE\BasisFunction.cs" />
    <Compile Include="Dxf\GTE\BezierCurve.cs" />
    <Compile Include="Dxf\GTE\BSplineCurve.cs" />
    <Compile Include="Dxf\GTE\BSplineCurveFit.cs" />
    <Compile Include="Dxf\GTE\BSplineReduction.cs" />
    <Compile Include="Dxf\GTE\BSplineSurface.cs" />
    <Compile Include="Dxf\GTE\BSplineSurfaceFit.cs" />
    <Compile Include="Dxf\GTE\GaussianElimination.cs" />
    <Compile Include="Dxf\GTE\GMatrix.cs" />
    <Compile Include="Dxf\GTE\GTE.cs" />
    <Compile Include="Dxf\GTE\GVector.cs" />
    <Compile Include="Dxf\GTE\Integration.cs" />
    <Compile Include="Dxf\GTE\IntrIntervals.cs" />
    <Compile Include="Dxf\GTE\LexicoArray2.cs" />
    <Compile Include="Dxf\GTE\NURBSCurve.cs" />
    <Compile Include="Dxf\GTE\NURBSSurface.cs" />
    <Compile Include="Dxf\GTE\ParametricCurve.cs" />
    <Compile Include="Dxf\GTE\ParametricSurface.cs" />
    <Compile Include="Dxf\GTE\RootBisection.cs" />
    <Compile Include="Dxf\GTE\RootsPolynominal.cs" />
    <Compile Include="Dxf\Header\AttMode.cs" />
    <Compile Include="Dxf\Header\DxfVersion.cs" />
    <Compile Include="Dxf\Header\HeaderVariable.cs" />
    <Compile Include="Dxf\Header\HeaderVariableCode.cs" />
    <Compile Include="Dxf\Header\HeaderVariables.cs" />
    <Compile Include="Dxf\Header\PointShape.cs" />
    <Compile Include="Dxf\IO\BinaryCodeValueReader.cs" />
    <Compile Include="Dxf\IO\BinaryCodeValueWriter.cs" />
    <Compile Include="Dxf\IO\DxfReader.cs" />
    <Compile Include="Dxf\IO\DxfVersionNotSupportedException.cs" />
    <Compile Include="Dxf\IO\DxfWriter.cs" />
    <Compile Include="Dxf\IO\ICodeValueReader.cs" />
    <Compile Include="Dxf\IO\ICodeValueWriter.cs" />
    <Compile Include="Dxf\IO\TextCodeValueReader.cs" />
    <Compile Include="Dxf\IO\TextCodeValueWriter.cs" />
    <Compile Include="Dxf\Lineweight.cs" />
    <Compile Include="Dxf\MathHelper.cs" />
    <Compile Include="Dxf\Matrix2.cs" />
    <Compile Include="Dxf\Matrix3.cs" />
    <Compile Include="Dxf\Matrix4.cs" />
    <Compile Include="Dxf\Objects\DictionaryCloningFlags.cs" />
    <Compile Include="Dxf\Objects\DictionaryObject.cs" />
    <Compile Include="Dxf\Objects\Group.cs" />
    <Compile Include="Dxf\Objects\GroupEntityChangeEventArgs.cs" />
    <Compile Include="Dxf\Objects\ImageDefinition.cs" />
    <Compile Include="Dxf\Objects\ImageDefinitionReactor.cs" />
    <Compile Include="Dxf\Objects\ImageDisplayQuality.cs" />
    <Compile Include="Dxf\Objects\LayerPropertiesFlags.cs" />
    <Compile Include="Dxf\Objects\LayerPropertiesRestoreFlags.cs" />
    <Compile Include="Dxf\Objects\LayerState.cs" />
    <Compile Include="Dxf\Objects\LayerStateProperties.cs" />
    <Compile Include="Dxf\Objects\Layout.cs" />
    <Compile Include="Dxf\Objects\MLineStyle.cs" />
    <Compile Include="Dxf\Objects\MLineStyleElement.cs" />
    <Compile Include="Dxf\Objects\MLineStyleElementChangeEventArgs.cs" />
    <Compile Include="Dxf\Objects\MLineStyleFlags.cs" />
    <Compile Include="Dxf\Objects\PaperMargin.cs" />
    <Compile Include="Dxf\Objects\PlotFlags.cs" />
    <Compile Include="Dxf\Objects\PlotPaperUnits.cs" />
    <Compile Include="Dxf\Objects\PlotRotation.cs" />
    <Compile Include="Dxf\Objects\PlotSettings.cs" />
    <Compile Include="Dxf\Objects\PlotType.cs" />
    <Compile Include="Dxf\Objects\RasterVariables.cs" />
    <Compile Include="Dxf\Objects\ShadePlotMode.cs" />
    <Compile Include="Dxf\Objects\ShadePlotResolutionMode.cs" />
    <Compile Include="Dxf\Objects\SupportedImageFormats.cs" />
    <Compile Include="Dxf\Objects\UnderlayDefinition.cs" />
    <Compile Include="Dxf\Objects\UnderlayDgnDefinition.cs" />
    <Compile Include="Dxf\Objects\UnderlayDwfDefinition.cs" />
    <Compile Include="Dxf\Objects\UnderlayPdfDefinition.cs" />
    <Compile Include="Dxf\Objects\UnderlayType.cs" />
    <Compile Include="Dxf\Objects\XRecord.cs" />
    <Compile Include="Dxf\Objects\XRecordEntry.cs" />
    <Compile Include="Dxf\StringEnum.cs" />
    <Compile Include="Dxf\SubclassMarker.cs" />
    <Compile Include="Dxf\Symbols.cs" />
    <Compile Include="Dxf\Tables\ApplicationRegistry.cs" />
    <Compile Include="Dxf\Tables\DimensionStyle.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleAlternateUnits.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleFitOptions.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleFitTextMove.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleOverride.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleOverrideChangeEventArgs.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleOverrideType.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleTextDirection.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleTextHorizontalPlacement.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleTextVerticalPlacement.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleTolerances.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleTolerancesDisplayMethod.cs" />
    <Compile Include="Dxf\Tables\DimensionStyleTolerancesVerticalPlacement.cs" />
    <Compile Include="Dxf\Tables\FontStyle.cs" />
    <Compile Include="Dxf\Tables\Layer.cs" />
    <Compile Include="Dxf\Tables\LayerFlags.cs" />
    <Compile Include="Dxf\Tables\Linetype.cs" />
    <Compile Include="Dxf\Tables\LinetypeSegment.cs" />
    <Compile Include="Dxf\Tables\LinetypeSegmentChangeEventArgs.cs" />
    <Compile Include="Dxf\Tables\LinetypeSegmentRotationType.cs" />
    <Compile Include="Dxf\Tables\LinetypeSegmentType.cs" />
    <Compile Include="Dxf\Tables\LinetypeShapeSegment.cs" />
    <Compile Include="Dxf\Tables\LinetypeSimpleSegment.cs" />
    <Compile Include="Dxf\Tables\LinetypeTextSegment.cs" />
    <Compile Include="Dxf\Tables\ShapeStyle.cs" />
    <Compile Include="Dxf\Tables\TableObject.cs" />
    <Compile Include="Dxf\Tables\TableObjectChangedEventArgs.cs" />
    <Compile Include="Dxf\Tables\TextStyle.cs" />
    <Compile Include="Dxf\Tables\UCS.cs" />
    <Compile Include="Dxf\Tables\View.cs" />
    <Compile Include="Dxf\Tables\ViewMode.cs" />
    <Compile Include="Dxf\Tables\VPort.cs" />
    <Compile Include="Dxf\Transparency.cs" />
    <Compile Include="Dxf\Units\AngleDirection.cs" />
    <Compile Include="Dxf\Units\AngleUnitFormat.cs" />
    <Compile Include="Dxf\Units\AngleUnitType.cs" />
    <Compile Include="Dxf\Units\DrawingTime.cs" />
    <Compile Include="Dxf\Units\DrawingUnits.cs" />
    <Compile Include="Dxf\Units\FractionFormatType.cs" />
    <Compile Include="Dxf\Units\ImageResolutionUnits.cs" />
    <Compile Include="Dxf\Units\ImageUnits.cs" />
    <Compile Include="Dxf\Units\LinearUnitFormat.cs" />
    <Compile Include="Dxf\Units\LinearUnitType.cs" />
    <Compile Include="Dxf\Units\UnitHelper.cs" />
    <Compile Include="Dxf\Units\UnitStyleFormat.cs" />
    <Compile Include="Dxf\Vector2.cs" />
    <Compile Include="Dxf\Vector3.cs" />
    <Compile Include="Dxf\Vector4.cs" />
    <Compile Include="Dxf\XData.cs" />
    <Compile Include="Dxf\XDataCode.cs" />
    <Compile Include="Dxf\XDataRecord.cs" />
    <Compile Include="Entitys\BoundingBox.cs" />
    <Compile Include="Entitys\EntityArc.cs" />
    <Compile Include="Entitys\EntityBlock.cs" />
    <Compile Include="Entitys\EntityCircle.cs" />
    <Compile Include="Entitys\EntityCmd.cs" />
    <Compile Include="Entitys\EntityGroup.cs" />
    <Compile Include="Entitys\EntityLayer.cs" />
    <Compile Include="Entitys\EntityLine.cs" />
    <Compile Include="Entitys\EntityPen.cs" />
    <Compile Include="Entitys\EntityPoint.cs" />
    <Compile Include="Entitys\EntityPolyline2D.cs" />
    <Compile Include="Entitys\EntitySpline.cs" />
    <Compile Include="Entitys\EntityLwPolyline.cs" />
    <Compile Include="Entitys\EntityRectangle.cs" />
    <Compile Include="Entitys\EntityText.cs" />
    <Compile Include="Entitys\IEntity.cs" />
    <Compile Include="Entitys\LwPolyLineVertex.cs" />
    <Compile Include="Entitys\Offset.cs" />
    <Compile Include="ImageExtension.cs" />
    <Compile Include="Input\DynamicInputer.cs" />
    <Compile Include="Input\DynamicInputTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Input\DynInputCtrl.cs" />
    <Compile Include="Input\DynInputDouble.cs" />
    <Compile Include="Input\DynInputInteger.cs" />
    <Compile Include="Input\DynInputPoint.cs" />
    <Compile Include="Input\DynInputResult.cs" />
    <Compile Include="Input\DynInputStatus.cs" />
    <Compile Include="Input\DynInputString.cs" />
    <Compile Include="Input\DynInputTextBoxOne.cs" />
    <Compile Include="Interfaces\ICanvas.cs" />
    <Compile Include="Interfaces\ICommand.cs" />
    <Compile Include="Interfaces\IDocument.cs" />
    <Compile Include="Core\IEntity.cs" />
    <Compile Include="Interfaces\IGraphicsContext.cs" />
    <Compile Include="Interfaces\IMarkerable.cs" />
    <Compile Include="Interfaces\IRenderable.cs" />
    <Compile Include="Interfaces\IView.cs" />
    <Compile Include="Interfaces\TextAlignment.cs" />
    <Compile Include="Marker\ILaser.cs" />
    <Compile Include="Marker\IMark.cs" />
    <Compile Include="Marker\IMarker.cs" />
    <Compile Include="Marker\IMarkerArg.cs" />
    <Compile Include="Marker\IMatrixStack.cs" />
    <Compile Include="Marker\LaserVirtual.cs" />
    <Compile Include="Marker\MarkArg.cs" />
    <Compile Include="Marker\MarkerArgDefault.cs" />
    <Compile Include="Marker\MarkerArgPmac.cs" />
    <Compile Include="Marker\MarkerCoordinate.cs" />
    <Compile Include="Marker\MarkerPmac.cs" />
    <Compile Include="Marker\MatrixStack.cs" />
    <Compile Include="UndoRedo\IUndoRedo.cs" />
    <Compile Include="UndoRedo\UndoRedoEntityAdd.cs" />
    <Compile Include="Action.cs" />
    <Compile Include="UndoRedo\UndoRedoEntityDelete.cs" />
    <Compile Include="UndoRedo\UndoRedoEntityGroup.cs" />
    <Compile Include="UndoRedo\UndoRedoEntityMove.cs" />
    <Compile Include="UndoRedo\UndoRedoEntitySort.cs" />
    <Compile Include="UndoRedo\UndoRedoEntityReverse.cs" />
    <Compile Include="UndoRedo\UndoRedoEntityUnGroup.cs" />
    <Compile Include="UndoRedo\UndoRedoMultiple.cs" />
    <Compile Include="UndoRedo\UndoRedoSingle.cs" />
    <Compile Include="ViewModels\CardSettingWindowViewModel.cs" />
    <Compile Include="ViewModels\MarkerSettingWindowViewModel.cs" />
    <Compile Include="ViewModels\MarkingViewModel.cs" />
    <Compile Include="ViewModels\SkCanvasViewModel.cs" />
    <Compile Include="Views\BufferView.xaml.cs">
      <DependentUpon>BufferView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CardSettingWindow.xaml.cs">
      <DependentUpon>CardSettingWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomPropertyGrid.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Views\GLEditor.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Views\GLEditor.Designer.cs">
      <DependentUpon>GLEditor.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SettingWindow.xaml.cs">
      <DependentUpon>SettingWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\MarkingView.xaml.cs">
      <DependentUpon>MarkingView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\OptimizerWindow.xaml.cs">
      <DependentUpon>OptimizerWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\SkEditor.xaml.cs">
      <DependentUpon>SkEditor.xaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\GLEditor.resx">
      <DependentUpon>GLEditor.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="app.config" />
    <None Include="Dxf\GTE\README.md" />
    <None Include="OpenTK.dll.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Page Include="Views\BufferView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CardSettingWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Icons\Open.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\Icons\Save.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\SettingWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\MarkingView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\OptimizerWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SkEditor.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\redo.png" />
    <Resource Include="Views\Icons\Toolbars\undo.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\101.png" />
    <Resource Include="Views\Icons\Toolbars\102.png" />
    <Resource Include="Views\Icons\Toolbars\103.png" />
    <Resource Include="Views\Icons\Toolbars\104.png" />
    <Resource Include="Views\Icons\Toolbars\105.png" />
    <Resource Include="Views\Icons\Toolbars\106.png" />
    <Resource Include="Views\Icons\Toolbars\107.png" />
    <Resource Include="Views\Icons\Toolbars\108.png" />
    <Resource Include="Views\Icons\Toolbars\109.png" />
    <Resource Include="Views\Icons\Toolbars\110.png" />
    <Resource Include="Views\Icons\Toolbars\111.png" />
    <Resource Include="Views\Icons\Toolbars\112.png" />
    <Resource Include="Views\Icons\Toolbars\113.png" />
    <Resource Include="Views\Icons\Toolbars\114.png" />
    <Resource Include="Views\Icons\Toolbars\115.png" />
    <Resource Include="Views\Icons\Toolbars\116.png" />
    <Resource Include="Views\Icons\Toolbars\117.png" />
    <Resource Include="Views\Icons\Toolbars\118.png" />
    <Resource Include="Views\Icons\Toolbars\119.png" />
    <Resource Include="Views\Icons\Toolbars\120.png" />
    <Resource Include="Views\Icons\Toolbars\121.png" />
    <Resource Include="Views\Icons\Toolbars\122.png" />
    <Resource Include="Views\Icons\Toolbars\123.png" />
    <Resource Include="Views\Icons\Toolbars\124.png" />
    <Resource Include="Views\Icons\Toolbars\125.png" />
    <Resource Include="Views\Icons\Toolbars\126.png" />
    <Resource Include="Views\Icons\Toolbars\127.png" />
    <Resource Include="Views\Icons\Toolbars\128.png" />
    <Resource Include="Views\Icons\Toolbars\129.png" />
    <Resource Include="Views\Icons\Toolbars\130.png" />
    <Resource Include="Views\Icons\Toolbars\131.png" />
    <Resource Include="Views\Icons\Toolbars\132.png" />
    <Resource Include="Views\Icons\Toolbars\201.png" />
    <Resource Include="Views\Icons\Toolbars\202.png" />
    <Resource Include="Views\Icons\Toolbars\203.png" />
    <Resource Include="Views\Icons\Toolbars\204.png" />
    <Resource Include="Views\Icons\Toolbars\205.png" />
    <Resource Include="Views\Icons\Toolbars\206.png" />
    <Resource Include="Views\Icons\Toolbars\207.png" />
    <Resource Include="Views\Icons\Toolbars\208.png" />
    <Resource Include="Views\Icons\Toolbars\209.png" />
    <Resource Include="Views\Icons\Toolbars\210.png" />
    <Resource Include="Views\Icons\Toolbars\211.png" />
    <Resource Include="Views\Icons\Toolbars\212.png" />
    <Resource Include="Views\Icons\Toolbars\213.png" />
    <Resource Include="Views\Icons\Toolbars\214.png" />
    <Resource Include="Views\Icons\Toolbars\215.png" />
    <Resource Include="Views\Icons\Toolbars\216.png" />
    <Resource Include="Views\Icons\Toolbars\217.png" />
    <Resource Include="Views\Icons\Toolbars\218.png" />
    <Resource Include="Views\Icons\Toolbars\219.png" />
    <Resource Include="Views\Icons\Toolbars\220.png" />
    <Resource Include="Views\Icons\Toolbars\221.png" />
    <Resource Include="Views\Icons\Toolbars\222.png" />
    <Resource Include="Views\Icons\Toolbars\223.png" />
    <Resource Include="Views\Icons\Toolbars\224.png" />
    <Resource Include="Views\Icons\Toolbars\225.png" />
    <Resource Include="Views\Icons\Toolbars\226.png" />
    <Resource Include="Views\Icons\Toolbars\227.png" />
    <Resource Include="Views\Icons\Toolbars\228.png" />
    <Resource Include="Views\Icons\Toolbars\229.png" />
    <Resource Include="Views\Icons\Toolbars\230.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Dxf\netDxf.xml" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\line.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\delete.png" />
    <Resource Include="Views\Icons\Toolbars\shape_group.png" />
    <Resource Include="Views\Icons\Toolbars\shape_ungroup.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\layer.png" />
    <Resource Include="Views\Icons\Toolbars\layer2.png" />
    <Resource Include="Views\Icons\Toolbars\layers.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\Optimized_On.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\img_dxf.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\copy_hover.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\copy.png" />
    <Resource Include="Views\Icons\Toolbars\paste.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\cut.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\simul_delete.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Views\Icons\Toolbars\Reverse.png" />
    <Resource Include="Views\Icons\Toolbars\reverssort.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\McLaser.Devices.Motion\McLaser.Devices.Motion.csproj">
      <Project>{A3B4C5D6-E7F8-A9B0-C1D2-E3F4A5B6C7D8}</Project>
      <Name>McLaser.Devices.Motion</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>