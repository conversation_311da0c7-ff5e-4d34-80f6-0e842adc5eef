﻿<UserControl x:Class="McLaser.EditViewerSk.Views.MarkingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:McLaser.EditViewerSk.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <StackPanel Orientation="Vertical">
            <Border BorderThickness="2" BorderBrush="Brown">
                 <StackPanel Orientation="Horizontal" Margin="10 10 10 10">
                    <Button Command="{Binding MarkFullCommand}" Content="全图加工" Width="60" Height="30" Margin="5" />
                    <Button Command="{Binding MarkSelectedCommand}" Content="选中加工" Width="60" Height="30" Margin="5"/>
                    <Button Command="{Binding MarkPauseCommand}" Content="暂停" Width="60" Height="30" Margin="5"/>
                    <Button Command="{Binding MarkStopCommand}" Content="停止" Width="60" Height="30" Margin="5"/>
                    <CheckBox IsChecked="{Binding IsSimulationCommand}" Content="模拟加工" Width="80" Height="30" Margin="5"  VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
                </StackPanel>
            </Border>

            <Border BorderThickness="2" BorderBrush="Brown">
                <StackPanel Orientation="Horizontal" Margin="10 10 10 10">
                    <Button  Command="{Binding OpenMarkerSettingCommand}" Content="加工参数" Width="60" Height="30" Margin="5"/>
                    <Button  Command="{Binding OpenCardSettingCommand}" Content="控制参数" Width="60" Height="30" Margin="5"/>
                </StackPanel>
            </Border>

            <GroupBox BorderThickness="2" BorderBrush="Brown" Header="PMAC卡测试">
                <StackPanel Orientation="Horizontal" Margin="10 10 10 10">
                    <Label Content="轴号"/>
                    <TextBox Text="{Binding HomeAxisNumber}" Width="80" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" Margin="0 3 10 3"/>
                    <Button Content="回零测试" Background="{Binding IsHomeCompleted}" Command="{Binding AxisHomeCommand}"/>
                </StackPanel>
            </GroupBox>
        </StackPanel>
    </Grid>
</UserControl>
