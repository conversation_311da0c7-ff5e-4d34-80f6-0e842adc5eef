﻿using SkiaSharp;
using Newtonsoft.Json;
using System.ComponentModel;



namespace McLaser.EditViewerSk.Base
{
    public class Config
    {
        public static double ZoomMax = 1e4;
        public static double ZoomMin = 1e-4;
        public static float ScaleIncrement = 0.1f;

        public static int DocumentGridInterval = 10;

        public static int UndoStackSize = 100;

        public static double AngleFactor = 1;
        public static string DocumentVersion = "V0.1";

        [Description("笔号颜色 \r默认: 10 Colors")]
        [JsonIgnore]
        [Browsable(true)]
        [ReadOnly(true)]
        [Category("Pen")]
        [DisplayName("Colors")]
        public static SKColor[] PenColors { get; } = new SKColor[]
        {
            SKColors.White,
            SKColors.Gray,
            SKColors.Orange,
            SKColors.Yellow,
            SKColors.Blue,
            SKColors.Magenta,
            SKColors.Cyan,
            SKColors.Orchid,
            SKColors.Red,
            SKColors.Green
        };

        [Category("View")]
        [DisplayName("Color (entity)")]
        [Description("Color of Default Entity\rDefault: White (PenColors[0])")]
        [JsonIgnore,Browsable(true), ReadOnly(false)]
        public static SKColor ViewDefaultEntityColor { get; set; } = Config.PenColors[0];
    }
}
