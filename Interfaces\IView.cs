﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using SkiaSharp.Views.Desktop;
using System;
using System.Numerics;
using System.Windows.Forms;


namespace McLaser.EditViewerSk.Interfaces
{
    public interface IView : IDisposable
    {
        DocumentBase Document { get; set; }

        void OnMouseDoubleClick(MouseEventArgs e);
 

        void OnCommand(ICommand cmd);

        float ModelToCanvas(double value);
        Vector2 ModelToCanvas(Vector2 pointInModel);
        float CanvasToModel(double value);
        Vector2 CanvasToModel(Vector2 pointInCanvas);
        void RepaintCanvas(bool bufferBitmapToRedraw = false);
        void OnZoomFit(BoundingBox br = null);

        SKPath SkPath { get; set; }
    }
}
