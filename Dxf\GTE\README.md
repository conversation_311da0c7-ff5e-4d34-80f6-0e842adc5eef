﻿The GTE namespace contains classes that have been translated to C# from the Geometric Tools Library original C++ code.

The Geometric Tool Library makes a lot of use of C++ templates and generics to handle, among other things, arbitrary vector dimensions and real numbers. This has no easy translation into C#, therefore the translated code has been limited to use three dimensional vectors (Vector3) with double precision.

Geometric Tools Library homepage https://www.geometrictools.com

Original license:<br>
<PERSON>, Geometric Tools, Redmond WA 98052<br>
Copyright (c) 1998-2022<br>
Distributed under the Boost Software License, Version 1.0.<br>
https://www.boost.org/LICENSE_1_0.txt<br>
https://www.geometrictools.com/License/Boost/LICENSE_1_0.txt<br>
Version: 6.0.2022.01.06