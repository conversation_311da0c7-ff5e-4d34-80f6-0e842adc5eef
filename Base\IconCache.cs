﻿using System.Collections.Generic;
using System.Windows.Media.Imaging;
using System;
using System.Windows.Media;


namespace McLaser.EditViewerSk.Base
{
    public static class IconCache
    {
        private static readonly Dictionary<string, ImageSource> IconDictionary = new();

        public static ImageSource GetIcon(string entityType)
        {
  
            if (!IconDictionary.ContainsKey(entityType))
            {
                IconDictionary[entityType] = LoadIconForEntityType(entityType);
            }
            return IconDictionary[entityType];
        }

        private static ImageSource LoadIconForEntityType(string entityType)
        {
            switch (entityType)
            {
                case "EntityLine":
                    return new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/line.png"));

                case "EntityArc":
                    return new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/219.png"));

                case "EntityCircle":
                    return new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/218.png"));

                case "EntityPoint":
                    return new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/201.png"));
                case "EntityGroup":
                    return new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/121.png"));
                case "EntityRectangle":
                    return new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/204.png"));
                case "EntityPolyline":
                    return new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/217.png"));
                case "EntityCmd":
                    return new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));
                case "EntityLayer":
                    return new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/layers.png"));
                default:
                    return null;
            }
        }
    }
}