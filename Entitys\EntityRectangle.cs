﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    public class EntityRectangle : EntityBase
    {
        public EntityRectangle()
        {
            Name = "Rectangle";
            Icon =  new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/204.png"));
        }

        private Vector2 _startPoint = new Vector2();
        [Category("基础"), DisplayName("起始点")]
        public Vector2 StartPoint
        {
            get { return _startPoint; }
            set { _startPoint = value; OnPropertyChanged("startPoint"); }
        }


        private Vector2 _endPoint = new Vector2();
        [Category("基础"), DisplayName("结束点")]
        public Vector2 EndPoint
        {
            get { return _endPoint; }
            set { _endPoint = value; OnPropertyChanged("endPoint"); }
        }

      

        private BoundingBox _boundingBox;
        public override BoundingBox BoundingBox
        {
            get
            {
                double left = this.StartPoint.X < this.EndPoint.X ? this.StartPoint.X : this.EndPoint.X;
                double right = this.StartPoint.X < this.EndPoint.X ? this.EndPoint.X : this.StartPoint.X;
                double top = this.StartPoint.Y > this.EndPoint.Y ? this.StartPoint.Y : this.EndPoint.Y;
                double bottom = this.StartPoint.Y > this.EndPoint.Y ? this.EndPoint.Y : this.StartPoint.Y;
                this._boundingBox = new BoundingBox(left, top, right, bottom);
                return this._boundingBox;
            }
        }



        public override void Render(IView view)
        {
            if (view == null)
            {
                return;
            }

            if (!this.IsRenderable)
            {
                return;
            }
            if (this.IsNeedToRegen)
            {
                this.Regen();
            }
            Pen.Color = IsSelected ? SKColors.Red : SKColors.Black;
            (view as ViewBase).DrawRectangle(_startPoint, Math.Abs(_startPoint.X - _endPoint.X), Math.Abs(_startPoint.Y - _endPoint.Y), Pen);
        }

        public override bool HitTest(double left, double top, double right, double bottom, double threshold)
        {
            return MathHelper.IntersectRectInRect(this.BoundingBox, new BoundingBox(left, top, right, bottom), threshold);
        }

        /// <summary>
        /// 对象捕捉点
        /// </summary>
        public override List<ObjectSnapPoint> GetSnapPoints()
        {
            List<ObjectSnapPoint> snapPnts = new List<ObjectSnapPoint>
            {
                new ObjectSnapPoint(ObjectSnapMode.End, _startPoint),
                new ObjectSnapPoint(ObjectSnapMode.End, _endPoint),
                new ObjectSnapPoint(ObjectSnapMode.End, new Vector2(_startPoint.X, _endPoint.Y)),
                new ObjectSnapPoint(ObjectSnapMode.End, new Vector2(_endPoint.X, _startPoint.Y))
            };
            return snapPnts;
        }

        public override void Translate(Vector2 translation)
        {
            throw new System.NotImplementedException();
        }

        
    }
}
