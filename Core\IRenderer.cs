
using SkiaSharp;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;

namespace McLaser.EditViewerSk.Core
{
    /// <summary>
    /// Defines the contract for a renderer that can draw entities onto a Skia canvas.
    /// </summary>
    public interface IRenderer
    {
        /// <summary>
        /// Adds a single entity to the renderer's cache.
        /// </summary>
        /// <param name="entity">The entity to add.</param>
        void AddEntity(EntityBase entity);

        /// <summary>
        /// Removes an entity from the renderer's cache.
        /// </summary>
        /// <param name="entity">The entity to remove.</param>
        void RemoveEntity(EntityBase entity);

        /// <summary>
        /// Updates the cached representation of an entity.
        /// </summary>
        /// <param name="entity">The entity to update.</param>
        void UpdateEntity(EntityBase entity);

        /// <summary>
        /// Clears all cached geometry.
        /// </summary>
        void Clear();

        /// <summary>
        /// Renders all cached entities onto the canvas.
        /// </summary>
        /// <param name="canvas">The Skia canvas to draw on.</param>
        void Render(SKCanvas canvas, SKRect visibleWorldRect);
    }
}
