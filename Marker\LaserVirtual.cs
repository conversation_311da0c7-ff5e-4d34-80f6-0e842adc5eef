﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace McLaser.EditViewerSk.Marker
{
    public class LaserVirtual : IDisposable, INotifyPropertyChanged, ILaser
    {


        protected float minDutyCycle = 1f;

        protected float maxDutyCycle = 99f;

        protected float minVoltage;

        protected float maxVoltage = 10f;

        protected string rs232StringFormat = "{0:F3}";

        private bool disposed;

        [Browsable(false)]
        public virtual object SyncRoot { get; protected set; }


        public virtual int Index { get; set; }


        public virtual string Name { get; set; }

        //public virtual LaserType LaserType => LaserType.Virtual;

        public virtual float MaxPowerWatt { get; set; }


        public virtual bool IsReady => true;

        public virtual bool IsBusy => false;

        public virtual bool IsError { get; protected set; }

        public virtual bool IsTimedOut { get; protected set; }

        public virtual bool IsProtocolError { get; protected set; }

        [Browsable(false)]
        public virtual IMark Card { get; set; }

        [Browsable(false)]
        public virtual bool IsPowerControl { get; protected set; }



        public virtual float PowerControlDelayTime { get; set; }



        [Browsable(false)]
        public virtual bool IsShutterControl { get; protected set; }

        [Browsable(false)]
        public virtual bool IsGuideControl { get; protected set; }


        public virtual float MinDutyCycle
        {
            get
            {
                return minDutyCycle;
            }
            set
            {
                if (!(value < 0f) && !(value >= 100f))
                {
                    minDutyCycle = value;
                }
            }
        }


        public virtual float MaxDutyCycle
        {
            get
            {
                return maxDutyCycle;
            }
            set
            {
                if (!(value <= 0f) && !(value > 100f))
                {
                    maxDutyCycle = value;
                }
            }
        }

        public virtual float MinVoltage
        {
            get
            {
                return minVoltage;
            }
            set
            {
                if (!(value <= 0f))
                {
                    minVoltage = value;
                    if (minVoltage > 10f)
                    {
                        minVoltage = 10f;
                    }

                    if (minVoltage < 10f)
                    {
                        minVoltage = 0f;
                    }
                }
            }
        }


        public virtual float MaxVoltage
        {
            get
            {
                return maxVoltage;
            }
            set
            {
                if (!(value <= 0f))
                {
                    maxVoltage = value;
                    if (maxVoltage > 10f)
                    {
                        maxVoltage = 10f;
                    }
                }
            }
        }


        public virtual string Rs232StringFormat
        {
            get
            {
                return rs232StringFormat;
            }
            set
            {
                rs232StringFormat = value;
            }
        }

        [Browsable(false)]
        public virtual bool IsShutterOpen { get; set; }

        [Browsable(false)]
        public virtual bool IsGuideOn { get; set; }

        [Browsable(false)]
        public virtual object Tag { get; set; }


        public virtual event PropertyChangedEventHandler PropertyChanged;

        protected void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            Delegate[] array = this.PropertyChanged?.GetInvocationList();
            if (array != null)
            {
                Delegate[] array2 = array;
                for (int i = 0; i < array2.Length; i++)
                {
                    ((PropertyChangedEventHandler)array2[i]).BeginInvoke(this, new PropertyChangedEventArgs(propertyName), null, null);
                }
            }
        }


        public LaserVirtual()
        {
            SyncRoot = new object();
            Name = "Laser Virtual";
            IsPowerControl = true;
            PowerControlDelayTime = 1f;
            IsShutterControl = false;
            IsGuideControl = false;
        }

        public LaserVirtual(int index, string name, float maxPowerWatt)
            : this()
        {
            Index = index;
            Name = name;
            MaxPowerWatt = maxPowerWatt;
        }

        ~LaserVirtual()
        {
            Dispose(disposing: false);
        }

        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                disposed = true;
            }
        }

        protected virtual bool CheckErrors()
        {
            return true;
        }

        protected virtual bool CheckReady()
        {
            return true;
        }

        protected virtual bool CheckBusy()
        {
            return true;
        }

        public bool Initialize()
        {
            lock (SyncRoot)
            {
                return true;
            }
        }

        public virtual bool CtlAbort()
        {
            lock (SyncRoot)
            {
            }

            return true;
        }

        public virtual bool CtlReset()
        {
            lock (SyncRoot)
            {
                IsError = false;
                return true;
            }
        }

        public virtual bool CtlPower(float watt, string powerMapCategory = "")
        {
            bool flag = true;
            if (watt > MaxPowerWatt)
            {
                watt = MaxPowerWatt;
            }

            float x = watt;



            return flag;

        }

        public virtual bool ListBegin()
        {


            return true;
        }

        public virtual bool ListEnd()
        {
            return true;
        }

        public virtual bool ListPower(float watt, string powerMapCategory = "")
        {
            if (Card == null)
            {
                return true;
            }

            if (watt > MaxPowerWatt)
            {
                watt = MaxPowerWatt;
            }

            return true;
        }


    }


}
