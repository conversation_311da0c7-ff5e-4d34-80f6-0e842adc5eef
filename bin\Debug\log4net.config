﻿<?xml version="1.0" encoding="utf-8" ?>

<!-- This section contains the log4net configuration settings -->
<configuration>
	<!-- Level的级别，由高到低 -->
	<!-- OFF > Fatal > ERROR > WARN > DEBUG > INFO > ALL-->
	<!-- 解释：如果level是ERROR，则在cs文件里面调用log4net的info()方法，则不会写入到日志文件中-->
	<log4net>

		<!-- Setup the root category, add the appenders and set the default priority -->
		<!--在一个logger对象中的设置会覆盖根日志的设置。-->
		<!--而对Appender属性来说，子日志对象则会继承父日志对象的Appender列表。-->
		<!--这种缺省的行为方式也可以通过显式地设定<logger>标签的additivity属性为false而改变。Additivity的值缺省是true.-->
		<root>
			<level value="ALL"/>
			<appender-ref ref="LoggerAll" />
			<appender-ref ref="LoggerInfo" />
			<appender-ref ref="LoggerWarn" />
			<appender-ref ref="LoggerError" />
			<appender-ref ref="uiLogAppender" />
		</root>

		<!--<logger> 元素预定义了一个具体日志对象的设置。然后通过调用LogManager.GetLogger()函数，你可以检索具有该名字的日志。-->
		<!--如果LogManager.GetLogger(…)打开的不是预定义的日志对象，则该日志对象会继承根日志对象的属性。-->
		<!--知道了这一点，我们可以说，其实<logger>标签并不是必须的。-->
		<logger name="LoggerAll" additivity="false">
			<level value="ALL"/>
			<appender-ref ref="LoggerAll" />
		</logger>
		
		<logger name="LoggerInfo" additivity="false">
			<level value="ALL"/>
			<appender-ref ref="LoggerInfo" />
		</logger>

		<logger name="LoggerWarn" additivity="false">
			<level value="ALL"/>
			<appender-ref ref="LoggerWarn" />
		</logger>

		<logger name="LoggerError" additivity="false">
			<level value="ALL"/>
			<appender-ref ref="LoggerError" />
		</logger>

		<logger name="uiLogAppender" additivity="false">
			<level value="ALL"/>
			<appender-ref ref="uiLogAppender" />
		</logger>
		
		<appender name="LoggerAll" type="log4net.Appender.RollingFileAppender,log4net" >
			<!--日志输出到exe程序这个相对目录下-->
			<File value="MyLog\\All\\All" />
			<!--就是文件的编码，默认为Default，就是程序本身的编码-->
			<Encoding value="utf-8" />
			<!--输出的日志不会覆盖以前的信息-->
			<AppendToFile value="true" />
			<!--文件创建的方式-->
			<!--Size：按照文件的大小进行变换日志文件-->
			<!--Date：变换的形式为日期-->
			<!--Composite：混合使用日期和文件大小变换日志文件名-->
			<RollingStyle value="Composite" />
			<!--日志文件名-->
			<DatePattern value="_yyyy-MM-dd.'log'" />
			<!--最大文件大小-->
			<MaximumFileSize value="10MB" />
			<!--最大变换数量，-1为不限制-->
			<maxSizeRollBackups value="50" />
			<!--file number increase after the file's size exceeds the MaximumFileSize.-->
			<!--定义当前文件是最小编号的文件还是最大编号的文件。默认为-1，总是以更小的编号-->
			<!--<CountDirection value="1" />-->
			<!--是否使用静态文件名-->
			<!--StaticLogFileName的值如果为true，则当前日志文件(相对于历史日志文件而言)的文件名只取File参数。-->
			<!--如果为false，则文件名取File+DatePattern-->
			<StaticLogFileName value="false" />
			<!--多线程时采用最小锁定-->
			<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
			<filter type="log4net.Filter.LevelRangeFilter">
				<LevelMin value="ALL" />
				<LevelMax value="OFF" />
			</filter>
			<layout type="log4net.Layout.PatternLayout">
				<!--<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %logger - %message%newline" />-->
				<!--<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %C{1}.%M - %message%newline" />-->
        <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%-2thread] %-5level %C{1}.%M %L  %message%newline" />
			</layout>
		</appender>

		<appender name="LoggerInfo" type="log4net.Appender.RollingFileAppender,log4net" >
			<!--日志输出到exe程序这个相对目录下-->
			<File value="MyLog\\Info\\Info" />
			<!--就是文件的编码，默认为Default，就是程序本身的编码-->
			<Encoding value="utf-8" />
			<!--输出的日志不会覆盖以前的信息-->
			<AppendToFile value="true" />
			<!--文件创建的方式-->
			<!--Size：按照文件的大小进行变换日志文件-->
			<!--Date：变换的形式为日期-->
			<!--Composite：混合使用日期和文件大小变换日志文件名-->
			<RollingStyle value="Composite" />
			<!--日志文件名-->
			<DatePattern value="_yyyy-MM-dd.'log'" />
			<!--最大文件大小-->
			<MaximumFileSize value="10MB" />
			<!--最大变换数量，-1为不限制-->
			<maxSizeRollBackups value="50" />
			<!--file number increase after the file's size exceeds the MaximumFileSize.-->
			<!--定义当前文件是最小编号的文件还是最大编号的文件。默认为-1，总是以更小的编号-->
			<!--<CountDirection value="1" />-->
			<!--是否使用静态文件名-->
			<StaticLogFileName value="false" />
			<!--多线程时采用最小锁定-->
			<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
			<filter type="log4net.Filter.LevelRangeFilter">
				<LevelMin value="INFO" />
				<LevelMax value="INFO" />
			</filter>
			<layout type="log4net.Layout.PatternLayout">
				<!--<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %logger - %message%newline" />-->
				<!--<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %C{1}.%M - %message%newline" />-->
        <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%-2thread] %-5level %C{1}.%M %L  %message%newline" />
			</layout>
		</appender>

		<appender name="LoggerWarn" type="log4net.Appender.RollingFileAppender,log4net" >
			<!--日志输出到exe程序这个相对目录下-->
			<File value="MyLog\\Warn\\Warn" />
			<!--就是文件的编码，默认为Default，就是程序本身的编码-->
			<Encoding value="utf-8" />
			<!--输出的日志不会覆盖以前的信息-->
			<AppendToFile value="true" />
			<!--文件创建的方式-->
			<!--Size：按照文件的大小进行变换日志文件-->
			<!--Date：变换的形式为日期-->
			<!--Composite：混合使用日期和文件大小变换日志文件名-->
			<RollingStyle value="Composite" />
			<!--日志文件名-->
			<DatePattern value="_yyyy-MM-dd.'log'" />
			<!--最大文件大小-->
			<MaximumFileSize value="10MB" />
			<!--最大变换数量，-1为不限制-->
			<maxSizeRollBackups value="50" />
			<!--file number increase after the file's size exceeds the MaximumFileSize.-->
			<!--定义当前文件是最小编号的文件还是最大编号的文件。默认为-1，总是以更小的编号-->
			<!--<CountDirection value="1" />-->
			<!--是否使用静态文件名-->
			<StaticLogFileName value="false" />
			<!--多线程时采用最小锁定-->
			<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
			<filter type="log4net.Filter.LevelRangeFilter">
				<LevelMin value="WARN" />
				<LevelMax value="WARN" />
			</filter>
			<layout type="log4net.Layout.PatternLayout">
				<!--<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %logger - %message%newline" />-->
				<!--<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %C{1}.%M - %message%newline" />-->
        <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%-2thread] %-5level %C{1}.%M %L  %message%newline" />
			</layout>
		</appender>

		<appender name="LoggerError" type="log4net.Appender.RollingFileAppender,log4net" >
			<!--日志输出到exe程序这个相对目录下-->
			<File value="MyLog\\Error\\Error" />
			<!--就是文件的编码，默认为Default，就是程序本身的编码-->
			<Encoding value="utf-8" />
			<!--输出的日志不会覆盖以前的信息-->
			<AppendToFile value="true" />
			<!--文件创建的方式-->
			<!--Size：按照文件的大小进行变换日志文件-->
			<!--Date：变换的形式为日期-->
			<!--Composite：混合使用日期和文件大小变换日志文件名-->
			<RollingStyle value="Composite" />
			<!--日志文件名-->
			<DatePattern value="_yyyy-MM-dd.'log'" />
			<!--最大文件大小-->
			<MaximumFileSize value="10MB" />
			<!--最大变换数量，-1为不限制-->
			<maxSizeRollBackups value="50" />
			<!--file number increase after the file's size exceeds the MaximumFileSize.-->
			<!--定义当前文件是最小编号的文件还是最大编号的文件。默认为-1，总是以更小的编号-->
			<!--<CountDirection value="1" />-->
			<!--是否使用静态文件名-->
			<StaticLogFileName value="false" />
			<!--多线程时采用最小锁定-->
			<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
			<filter type="log4net.Filter.LevelRangeFilter">
				<LevelMin value="ERROR" />
				<LevelMax value="ERROR" />
			</filter>
			<layout type="log4net.Layout.PatternLayout">
				<!--<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %logger - %message%newline" />-->
				<!--<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %C{1}.%M - %message%newline" />-->
        <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%-2thread] %-5level %C{1}.%M %L  %message%newline" />
			</layout>
		</appender>

		<appender name="uiLogAppender" type="ModuleLog.UiLogAppender,ModuleLog">
			<layout type="log4net.Layout.PatternLayout">
				<!--<conversionPattern value="%date{HH:mm:ss,fff} %-5level - %message" />-->
				<!--<conversionPattern value="%-5level|%date{HH:mm:ss.fff} %C{1}.%M  %message%newline" />-->
        <!--FormOutput-->
        <!--<conversionPattern value="%-5level|%date{HH:mm:ss.fff} %message%newline" />-->
        <!--FormOutput2-->
        <conversionPattern value="%date{HH:mm:ss.fff} %-5level %message%newline" />
			</layout>
			<threshold value="INFO" />
		</appender>
		
	</log4net>
</configuration>
