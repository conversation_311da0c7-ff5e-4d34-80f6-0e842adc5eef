﻿using System;
using System.Threading.Tasks;
using System.Windows.Input;

public class AsyncCommand : ICommand
{
    private readonly Func<Task> _execute;
    private readonly Func<bool> _canExecute;

    public AsyncCommand(Func<Task> execute, Func<bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public bool CanExecute(object parameter)
    {
        return _canExecute?.Invoke() ?? true;
    }

    public async void Execute(object parameter)
    {
        await ExecuteAsync(parameter);
    }

    public async Task ExecuteAsync(object parameter)
    {
        if (CanExecute(parameter))
        {
            await _execute();
        }
    }

    public event EventHandler CanExecuteChanged
    {
        add => CommandManager.RequerySuggested += value;
        remove => CommandManager.RequerySuggested -= value;
    }
}
