﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using McLaser.Entities;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    public class EntityPolyline2D : EntityBase
    {

        public List<Vector2> Points { get; set; } = new();
        public List<double> Bulges { get; set; } = new();
        private List<EntityBase> entities = new List<EntityBase>();

        public List<EntityBase> Entities
        {
            get { return entities; }
            set { entities = value; }
        }



        private bool isClosed;
        [Browsable(true), ReadOnly(false), Category("基础"), DisplayName("是否闭合"), RefreshProperties(RefreshProperties.All)]
        public bool IsClosed
        {
            get => this.isClosed;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                this.isClosed = value;
                this.IsNeedToRegen = true;
            }
        }

        


        public EntityPolyline2D()
        {
            Name = "Polyline2D";
            Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/217.png"));
        }




        private List<Polyline2DVertex> _vertexes = new List<Polyline2DVertex>();
        public List<Polyline2DVertex> Vertexes
        {
            get { return _vertexes; }
            set { _vertexes = value; this.IsNeedToRegen = true; }
        }



        private void RegenVertextList()
        {

        }



        private void RegenBoundRect()
        {
            base.BoundingBox.Clear();
            foreach (var entity in this.Entities)
            {
                entity.Regen();
                base.BoundingBox.Union(entity.BoundingBox);
            }
        }



        public override void Regen()
        {
            this.RegenVertextList();
            this.RegenBoundRect();
            this.IsNeedToRegen = false;
        }



        public override void Render(IView view)
        {
            if (view == null)
            {
                return;
            }

            if (!this.IsRenderable)
            {
                return;
            }
            if (this.IsNeedToRegen)
            {
                this.Regen();
            }
            Pen.Color = IsSelected ? SKColors.Red : SKColors.Black;
            foreach (var item in entities)
            {
                item.Render(view);
            }
        }

      
    }
}
