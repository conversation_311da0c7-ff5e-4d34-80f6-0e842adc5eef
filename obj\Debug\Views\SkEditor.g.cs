﻿#pragma checksum "..\..\..\Views\SkEditor.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "0066CE61B50AD18B7D2AB95624847B5485A2235B813157FCD391DCFE50E87E94"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using McLaser.EditViewerSk;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.ViewModels;
using McLaser.EditViewerSk.Views;
using PropertyTools.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace McLaser.EditViewerSk.Views {
    
    
    /// <summary>
    /// SkEditor
    /// </summary>
    public partial class SkEditor : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 67 "..\..\..\Views\SkEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal PropertyTools.Wpf.TreeListBox tv;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\Views\SkEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Forms.Integration.WindowsFormsHost WinFormsHost;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\Views\SkEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Forms.PropertyGrid wfpg;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/McLaser.EditViewerSk;component/views/skeditor.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\SkEditor.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.tv = ((PropertyTools.Wpf.TreeListBox)(target));
            
            #line 71 "..\..\..\Views\SkEditor.xaml"
            this.tv.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.tv_MouseLeftButtonUp);
            
            #line default
            #line hidden
            
            #line 71 "..\..\..\Views\SkEditor.xaml"
            this.tv.KeyUp += new System.Windows.Input.KeyEventHandler(this.tv_KeyUp);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 74 "..\..\..\Views\SkEditor.xaml"
            ((PropertyTools.Wpf.EnumMenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertEntityCmd_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 75 "..\..\..\Views\SkEditor.xaml"
            ((PropertyTools.Wpf.EnumMenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertEntityCmd_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 76 "..\..\..\Views\SkEditor.xaml"
            ((PropertyTools.Wpf.EnumMenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertEntityCmd_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 77 "..\..\..\Views\SkEditor.xaml"
            ((PropertyTools.Wpf.EnumMenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertEntityCmd_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 78 "..\..\..\Views\SkEditor.xaml"
            ((PropertyTools.Wpf.EnumMenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertEntityCmd_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 79 "..\..\..\Views\SkEditor.xaml"
            ((PropertyTools.Wpf.EnumMenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertEntityCmd_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 101 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnNew_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 102 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnOpen_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 103 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnSave_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 104 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnUndo_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 105 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnRedo_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 106 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnCopy_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 107 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnCut_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 108 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnPaste_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 110 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnDelete_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 111 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnGroup_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 112 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnUnGroup_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 113 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnSort_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 114 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnReverse_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 168 "..\..\..\Views\SkEditor.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnLoadDxf_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.WinFormsHost = ((System.Windows.Forms.Integration.WindowsFormsHost)(target));
            return;
            case 23:
            this.wfpg = ((System.Windows.Forms.PropertyGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

