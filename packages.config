﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="DotNetProjects.Extended.Wpf.Toolkit" version="5.0.124" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="OpenTK" version="3.1.0" targetFramework="net48" />
  <package id="OpenTK.GLControl" version="3.1.0" targetFramework="net48" />
  <package id="PropertyTools" version="3.1.0" targetFramework="net48" />
  <package id="PropertyTools.Wpf" version="3.1.0" targetFramework="net48" />
  <package id="SkiaSharp" version="3.116.1" targetFramework="net48" />
  <package id="SkiaSharp.NativeAssets.macOS" version="3.116.1" targetFramework="net48" />
  <package id="SkiaSharp.NativeAssets.Win32" version="3.116.1" targetFramework="net48" />
  <package id="SkiaSharp.Views.Desktop.Common" version="3.116.1" targetFramework="net48" />
  <package id="SkiaSharp.Views.WindowsForms" version="3.116.1" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Drawing.Common" version="4.7.3" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net48" />
</packages>