﻿using Newtonsoft.Json;
using System;
using System.ComponentModel;
using System.Numerics;
 

namespace McLaser.EditViewerSk.Entitys
{
    [JsonObject]
    public struct Offset
    {

        public double X { get; set; }


        public double Y { get; set; }


        public double Angle { get; set; }

        [Browsable(false)]
        [ReadOnly(false)]
        public object Tag { get; set; }

        public Offset(double x, double y, double angle = 0.0f)
        {
            this.X = x;
            this.Y = y;
            this.Angle = angle;
            this.Tag = (object)null;
        }

        public Offset(Vector2 v, double angle = 0.0f)
        {
            this.X = v.X;
            this.Y = v.Y;
            this.Angle = angle;
            this.Tag = (object)null;
        }

        public Offset Clone() => new Offset()
        {
            X = this.X,
            Y = this.Y,
            Angle = this.Angle,
            Tag = this.Tag
        };

        public static Offset Translate(Offset o, float dx, float dy) => new Offset(o.X + dx, o.Y + dy, o.Angle);

        public static Offset Translate(Offset o, Vector2 delta) => new Offset(o.X + delta.X, o.Y + delta.Y, o.Angle);

        //public static Offset Rotate(Offset o, float angle, Vector2 rotateCenter) => new Offset(Vector2.Transform(o.ToVector2, Matrix3x2.CreateRotation(angle * ((float)Math.PI / 180f), rotateCenter)), o.Angle + angle);

        public static Offset Scale(Offset o, Vector2 scale) => new Offset(o.ToVector2 * scale, o.Angle);

        public static Offset Scale(Offset o, Vector2 scale, Vector2 center) => new Offset((o.ToVector2 - center) * scale + center, o.Angle);

        [JsonIgnore]
        [Browsable(false)]
        public static Offset Zero => new Offset(0.0f, 0.0f);

        [JsonIgnore]
        [Browsable(false)]
        public Vector2 ToVector2 => new Vector2(this.X, this.Y);

        //[JsonIgnore]
        //[Browsable(false)]
        //public Matrix3 ToMatrix => Matrix3.CreateTranslation(this.X, this.Y) * Matrix3x2.CreateRotation((float)Math.PI / 180f * this.Angle);

        public override string ToString() => string.Format("{0:F3}, {1:F3}, {2:F3}", (object)this.X, (object)this.Y, (object)this.Angle);
    }
}
