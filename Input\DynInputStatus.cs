﻿namespace McLaser.EditViewerSk.Input
{
    /// <summary>
    /// 动态输入结果
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class DynInputResult<T> : DynInputResult
    {
        /// <summary>
        /// 值
        /// </summary>
        protected T _value;
        public T value
        {
            get { return _value; }
        }

        public DynInputResult(DynInputStatus status, T value)
            : base(status)
        {
            _value = value;
        }
    }

    public enum DynInputStatus
    {
        OK = 0,
        Cancel = 1,
        Error = 2,
    }


}
