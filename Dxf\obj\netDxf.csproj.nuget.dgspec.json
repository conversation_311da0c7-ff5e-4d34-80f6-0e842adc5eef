{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.EditViewerSk\\Dxf\\netDxf.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.EditViewerSk\\Dxf\\netDxf.csproj": {"version": "3.0.1", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.EditViewerSk\\Dxf\\netDxf.csproj", "projectName": "netDxf", "projectPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.EditViewerSk\\Dxf\\netDxf.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.EditViewerSk\\Dxf\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48", "net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}, "net48": {"targetAlias": "net48", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}