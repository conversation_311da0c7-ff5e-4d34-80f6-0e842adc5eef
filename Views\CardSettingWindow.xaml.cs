﻿using McLaser.Core.Services.DeviceService.Base.Motion;
using McLaser.EditViewerSk.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace McLaser.EditViewerSk.Views
{
 
    public partial class CardSettingWindow : Window
    {
        public CardSettingWindow(IMotionCard card)
        {
            InitializeComponent();
            DataContext = new CardSettingWindowViewModel(card);
        }
    }
}
