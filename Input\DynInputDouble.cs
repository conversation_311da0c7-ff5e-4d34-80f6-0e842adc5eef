﻿using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Input
{
    public class DynInputDouble : DynInputTextBoxOne<double>
    {
        /// <summary>
        /// 更新值
        /// </summary>
        protected override bool UpdateValue()
        {
            return double.TryParse(_textBox.Text, out _value);
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public DynInputDouble(ViewBase presenter, double value)
            : base(presenter, value.ToString())
        {
        }
    }
}
