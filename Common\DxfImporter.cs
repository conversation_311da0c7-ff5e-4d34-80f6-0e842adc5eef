
using McLaser.EditViewerSk.Entitys;
using McLaser.Entities;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using WinVector2 = System.Windows.Vector;

namespace McLaser.EditViewerSk.Common
{
    public class DxfImportResult
    {
        public List<EntityBase> Entities { get; set; }
        public SKRect BoundingBox { get; set; }
    }

    public static class DxfImporter
    {
        public static async Task<DxfImportResult> ImportAsync(string filePath, Action<double> onProgress)
        {
            return await Task.Run(() =>
            {
                var entities = new List<EntityBase>();
                var boundingBox = SKRect.Empty;

                try
                {
                    DxfDocument dxf = DxfDocument.Load(filePath);
                    if (dxf == null) return null;

                    var modelSpace = dxf.Blocks["*Model_Space"];
                    if (modelSpace == null) return null;

                    int totalEntities = modelSpace.Entities.Count;
                    int processedCount = 0;

                    foreach (var entity in modelSpace.Entities)
                    {
                        EntityBase newEntity = null;
                        switch (entity.Type)
                        {
                            case EntityType.Line:
                                newEntity = CreateLine(entity as Line);
                                break;
                            case EntityType.Circle:
                                newEntity = CreateCircle(entity as Circle);
                                break;
                            case EntityType.Arc:
                                newEntity = CreateArc(entity as Arc);
                                break;
                            case EntityType.Polyline2D:
                                newEntity = CreatePolyline(entity as Polyline2D);
                                break;
                            // Add other entity types as needed
                        }

                        if (newEntity != null)
                        {
                            newEntity.Regen(); // Ensure BBox is calculated
                            entities.Add(newEntity);
                            var entityBounds = newEntity.BoundsSK;
                            if (boundingBox.IsEmpty)
                            {
                                boundingBox = entityBounds;
                            }
                            else
                            {
                                boundingBox.Union(entityBounds);
                            }
                        }

                        processedCount++;
                        if (processedCount % 1000 == 0) // Report progress every 1000 entities
                        {
                           onProgress?.Invoke((double)processedCount / totalEntities * 100.0);
                        }
                    }
                    onProgress?.Invoke(100.0);
                    return new DxfImportResult { Entities = entities, BoundingBox = boundingBox };
                }
                catch (Exception ex)
                {
                    // Log the exception
                    return null;
                }
            });
        }

        private static EntityLine CreateLine(Line e)
        {
            if (e == null) return null;
            return new EntityLine(e.StartPoint.X, e.StartPoint.Y, e.EndPoint.X, e.EndPoint.Y);
        }

        private static EntityCircle CreateCircle(Circle e)
        {
            if (e == null) return null;
            return new EntityCircle(new Vector2( e.Center.X, e.Center.Y), e.Radius);
        }

        private static EntityArc CreateArc(Arc e)
        {
            if (e == null) return null;
            double sweep = e.EndAngle - e.StartAngle;
            if (sweep < 0) sweep += 360;
            return new EntityArc(e.Center.X, e.Center.Y, e.Radius, e.StartAngle, sweep);
        }

        private static EntityPolyline2D CreatePolyline(Polyline2D e)
        {
            if (e == null) return null;
            EntityPolyline2D polyline = new EntityPolyline2D { IsClosed = e.IsClosed };

            foreach (var v in e.Vertexes)
            {
                polyline.Points.Add(new Vector2(v.Position.X, v.Position.Y));
                polyline.Bulges.Add(v.Bulge);
            }

            for (int i = 0; i < polyline.Points.Count - 1; i++)
            {
                var start = polyline.Points[i];
                var end = polyline.Points[i + 1];
                var bulge = polyline.Bulges[i];

                if (bulge == 0)
                {
                    EntityLine line = new EntityLine(start.X, start.Y, end.X, end.Y);
                    polyline.Entities.Add(line);
                }
                else
                {
                    double angle = 4 * Math.Atan(Math.Abs(bulge));
                    double distance = Math.Sqrt(Math.Pow(end.X - start.X, 2) + Math.Pow(end.Y - start.Y, 2));
                    double radius = distance / (2 * Math.Sin(angle / 2));

                    double a = Math.Atan2(end.Y - start.Y, end.X - start.X);
                    double b = (Math.PI - angle) / 2;
                    double c = a - b * (bulge > 0 ? 1 : -1);

                    double centerX = start.X + radius * Math.Cos(c);
                    double centerY = start.Y + radius * Math.Sin(c);

                    double startAngle = Math.Atan2(start.Y - centerY, start.X - centerX) * (180 / Math.PI);
                    double endAngle = Math.Atan2(end.Y - centerY, end.X - centerX) * (180 / Math.PI);
                    double sweepAngle = angle * (180 / Math.PI);

                    if (bulge < 0)
                    {
                        var temp = startAngle;
                        startAngle = endAngle;
                        endAngle = temp;
                    }

                    EntityArc arc = new EntityArc(centerX, centerY, radius, startAngle, sweepAngle);
                    polyline.Entities.Add(arc);
                }
            }
            return polyline;
        }
    }

    public static class RectExtensions
    {
        public static SKRect ToSKRect(this Rect rect)
        {
            if (rect == Rect.Empty) return SKRect.Empty;
            return new SKRect((float)rect.Left, (float)rect.Top, (float)rect.Right, (float)rect.Bottom);
        }
    }
}
