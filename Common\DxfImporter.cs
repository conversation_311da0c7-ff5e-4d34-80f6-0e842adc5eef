
using McLaser.EditViewerSk.Entitys;
using McLaser.Entities;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using WinVector2 = System.Windows.Vector;

namespace McLaser.EditViewerSk.Common
{
    public class DxfImportResult
    {
        public List<EntityBase> Entities { get; set; }
        public SKRect BoundingBox { get; set; }
    }

    public static class DxfImporter
    {
        public static async Task<DxfImportResult> ImportAsync(string filePath, Action<double> onProgress)
        {
            return await Task.Run(() =>
            {
                var entities = new List<EntityBase>();
                var boundingBox = SKRect.Empty;

                try
                {
                    System.Diagnostics.Debug.WriteLine($"开始加载DXF文件: {filePath}");

                    DxfDocument dxf = DxfDocument.Load(filePath);
                    if (dxf == null)
                    {
                        System.Diagnostics.Debug.WriteLine("DXF文档加载失败");
                        return null;
                    }

                    var modelSpace = dxf.Blocks["*Model_Space"];
                    if (modelSpace == null)
                    {
                        System.Diagnostics.Debug.WriteLine("未找到ModelSpace块");
                        return null;
                    }

                    int totalEntities = modelSpace.Entities.Count;
                    int processedCount = 0;
                    int skippedCount = 0;

                    System.Diagnostics.Debug.WriteLine($"开始处理 {totalEntities} 个图元");

                    foreach (var entity in modelSpace.Entities)
                    {
                        EntityBase newEntity = null;
                        try
                        {
                            switch (entity.Type)
                            {
                                case EntityType.Line:
                                    newEntity = CreateLine(entity as Line);
                                    break;
                                case EntityType.Circle:
                                    newEntity = CreateCircle(entity as Circle);
                                    break;
                                case EntityType.Arc:
                                    newEntity = CreateArc(entity as Arc);
                                    break;
                                case EntityType.Polyline2D:
                                    newEntity = CreatePolyline(entity as Polyline2D);
                                    break;
                                default:
                                    skippedCount++;
                                    break;
                            }

                            if (newEntity != null)
                            {
                                newEntity.Regen(); // Ensure BBox is calculated
                                entities.Add(newEntity);
                                var entityBounds = newEntity.BoundsSK;

                                // 调试：检查前几个图元的边界和状态
                                if (entities.Count <= 5)
                                {
                                    System.Diagnostics.Debug.WriteLine($"图元 {entities.Count}: {newEntity.GetType().Name}");
                                    System.Diagnostics.Debug.WriteLine($"  边界: {entityBounds}");
                                    System.Diagnostics.Debug.WriteLine($"  可渲染: {newEntity.IsRenderable}");
                                    System.Diagnostics.Debug.WriteLine($"  可见: {newEntity.IsVisible}");
                                    System.Diagnostics.Debug.WriteLine($"  需要重新生成: {newEntity.IsNeedToRegen}");
                                }

                                if (!entityBounds.IsEmpty)
                                {
                                    if (boundingBox.IsEmpty)
                                    {
                                        boundingBox = entityBounds;
                                    }
                                    else
                                    {
                                        boundingBox.Union(entityBounds);
                                    }
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine($"警告: 图元 {newEntity.GetType().Name} 的边界为空");
                                }
                            }
                        }
                        catch (Exception entityEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"处理图元时出错: {entityEx.Message}");
                            skippedCount++;
                        }

                        processedCount++;
                        if (processedCount % 5000 == 0) // Report progress every 5000 entities for better performance
                        {
                           onProgress?.Invoke((double)processedCount / totalEntities * 100.0);

                           // 定期强制垃圾回收以避免内存压力
                           if (processedCount % 50000 == 0)
                           {
                               GC.Collect(0, GCCollectionMode.Optimized);
                           }
                        }
                    }

                    onProgress?.Invoke(100.0);

                    System.Diagnostics.Debug.WriteLine($"处理完成: 成功 {entities.Count} 个，跳过 {skippedCount} 个");
                    System.Diagnostics.Debug.WriteLine($"最终边界框: {boundingBox}");

                    return new DxfImportResult { Entities = entities, BoundingBox = boundingBox };
                }
                catch (OutOfMemoryException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"内存不足: {ex.Message}");
                    throw; // 重新抛出内存异常
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"DXF导入异常: {ex}");
                    return null;
                }
            });
        }

        private static EntityLine CreateLine(Line e)
        {
            if (e == null) return null;
            return new EntityLine(e.StartPoint.X, e.StartPoint.Y, e.EndPoint.X, e.EndPoint.Y);
        }

        private static EntityCircle CreateCircle(Circle e)
        {
            if (e == null) return null;
            return new EntityCircle(new Vector2( e.Center.X, e.Center.Y), e.Radius);
        }

        private static EntityArc CreateArc(Arc e)
        {
            if (e == null) return null;
            double sweep = e.EndAngle - e.StartAngle;
            if (sweep < 0) sweep += 360;
            return new EntityArc(e.Center.X, e.Center.Y, e.Radius, e.StartAngle, sweep);
        }

        private static EntityPolyline2D CreatePolyline(Polyline2D e)
        {
            if (e == null) return null;
            EntityPolyline2D polyline = new EntityPolyline2D { IsClosed = e.IsClosed };

            foreach (var v in e.Vertexes)
            {
                polyline.Points.Add(new Vector2(v.Position.X, v.Position.Y));
                polyline.Bulges.Add(v.Bulge);
            }

            for (int i = 0; i < polyline.Points.Count - 1; i++)
            {
                var start = polyline.Points[i];
                var end = polyline.Points[i + 1];
                var bulge = polyline.Bulges[i];

                if (bulge == 0)
                {
                    EntityLine line = new EntityLine(start.X, start.Y, end.X, end.Y);
                    polyline.Entities.Add(line);
                }
                else
                {
                    double angle = 4 * Math.Atan(Math.Abs(bulge));
                    double distance = Math.Sqrt(Math.Pow(end.X - start.X, 2) + Math.Pow(end.Y - start.Y, 2));
                    double radius = distance / (2 * Math.Sin(angle / 2));

                    double a = Math.Atan2(end.Y - start.Y, end.X - start.X);
                    double b = (Math.PI - angle) / 2;
                    double c = a - b * (bulge > 0 ? 1 : -1);

                    double centerX = start.X + radius * Math.Cos(c);
                    double centerY = start.Y + radius * Math.Sin(c);

                    double startAngle = Math.Atan2(start.Y - centerY, start.X - centerX) * (180 / Math.PI);
                    double endAngle = Math.Atan2(end.Y - centerY, end.X - centerX) * (180 / Math.PI);
                    double sweepAngle = angle * (180 / Math.PI);

                    if (bulge < 0)
                    {
                        var temp = startAngle;
                        startAngle = endAngle;
                        endAngle = temp;
                    }

                    EntityArc arc = new EntityArc(centerX, centerY, radius, startAngle, sweepAngle);
                    polyline.Entities.Add(arc);
                }
            }
            return polyline;
        }
    }

    public static class RectExtensions
    {
        public static SKRect ToSKRect(this Rect rect)
        {
            if (rect == Rect.Empty) return SKRect.Empty;
            return new SKRect((float)rect.Left, (float)rect.Top, (float)rect.Right, (float)rect.Bottom);
        }
    }
}
