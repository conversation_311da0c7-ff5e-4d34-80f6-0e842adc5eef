﻿using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Commands
{
    public abstract class ModifyCmd : Command
    {
        /// <summary>
        /// 初始化
        /// </summary>
        public override void Initialize()
        {
            base.Initialize();

            this.pointer.bIsShowAnchor = false;
        }

        /// <summary>
        /// 结束
        /// </summary>
        public override void Terminate()
        {
            _mgr.Viewer.Selections.Clear();

            base.Terminate();
        }
    }
}
