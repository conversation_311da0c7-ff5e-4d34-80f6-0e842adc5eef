﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Core;
using McLaser.EditViewerSk.Interfaces;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace McLaser.EditViewerSk.Entitys
{

    //public interface IEntity : ICloneable
    //{
    //    object SyncRoot { get; }

    //    ulong Id { get; }

    //    string TypeName { get; }

    //    SKColor Color { get; set; }

    //    string Name { get; set; }

    //    string Description { get; set; }

    //    bool IsRenderable { get; set; }

    //    bool IsHitTestable { get; set; }

    //    bool IsSelected { get; set; }

    //    bool IsMarkerable { get; set; }

    //    bool IsNeedToRegen { get; set; }

    //    IEntity Parent { get; set; }

    //    bool IsHasChildren { get; }

    //    List<EntityBase> Children { get; }

    //    int ChildCount { get; }

    //    Vector2 In { get; }

    //    Vector2 Out { get; }

    //    BoundingBox BBox { get; set; }

    //    Alignments Alignment { get; set; }

    //    Vector2 ModelAlign { get; set; }
              
    //    Vector2 ModelTranslate { get; set; }
              
    //    Vector2 ModelScale { get; set; }
              
    //    Vector2 ModelRotate { get; set; }

    //    SKMatrix ModelMatrix { get; }

    //    object Tag { get; set; }

    //    bool IsEqual(SKColor color);

    //    void Regen();

    //    void RegenInOut();

    //    void RegenAlign();

    //    void Translate(double dx, double dy);

    //    void Translate(Vector2 deltaXy);

    //    void RotateX(double dAngle);

    //    void RotateY(double dAngle);

    //    void Rotate(double dAngleX, double dAngleY);

    //    void Rotate(Vector2 deltaAngle);

    //    void Scale(double deltaScale);

    //    void Scale(double dScaleX, double dScaleY);

    //    void Scale(Vector2 deltaScale);
    //    List<ObjectSnapPoint> GetSnapPoints();
    //    List<GripPoint> GetGripPoints();
    //    void SetGripPointAt(int index, GripPoint gripPoint, Vector2 newPosition);
    //    void Render(IView view);
    //}


    public enum Alignments
    {
        None = -1,
        Custom,
        TopLeft,
        TopCenter,
        TopRight,
        MiddleLeft,
        MiddleCenter,
        MiddleRight,
        BottomLeft,
        BottomCenter,
        BottomRight
    }
}
