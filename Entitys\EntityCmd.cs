﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Marker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Documents;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    public class EntityCmd : EntityBase
    {
        public string Buffer { get; set; }

        [Browsable(false)]
        public override MarkArg Arg { get; set; } 

        [Browsable(false)]
        public override double Angle { get; set; } 

        public override void Render(IView view)
        {

        }

        public override bool Mark(IMarkerArg markerArg, IMarkCommand cmd)
        {
            if (!this.IsMarkerable)
                return true;
            bool flag = true;

            flag &= cmd.MarkCmd(markerArg, this, out string buffer);
            if (!flag)
                return false;

            return flag;
        }
    }


    //测高
    public class EntityActionRange : EntityCmd
    {
        private bool enable;
        [Category("基础"), DisplayName("是否启用"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public bool IsEnable
        {
            get { return enable; }
            set { enable = value; OnPropertyChanged("IsEnable"); this.Name = enable ? "测距开" : "测距关"; }
        }


        public EntityActionRange()
        {
            this.Buffer = IsEnable ? "M2" : "M2.1";
            this.Name = IsEnable ? "测距开" : "测距关";
            Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));//new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));
        }
    }


    //C轴跟随
    public class EntityActionFollow : EntityCmd
    {

        private bool enable;
        [Category("基础"), DisplayName("是否启用"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public bool IsEnable
        {
            get { return enable; }
            set { enable = value; OnPropertyChanged("IsEnable"); this.Name = enable ? "C轴跟随开" : "C轴跟随关"; }
        }

        public EntityActionFollow()
        {
            this.Buffer = IsEnable ? "M6" : "M6.5";
            this.Name = IsEnable ? "C轴跟随开" : "C轴跟随关";
            Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));//new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));
        }
    }

    //Z轴补偿
    public class EntityActionCaliRange : EntityCmd
    {

        private bool enable;
        [Category("基础"), DisplayName("是否启用"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public bool IsEnable
        {
            get { return enable; }
            set { enable = value; OnPropertyChanged("IsEnable"); this.Name = enable ? "补偿开" : "补偿关"; }
        }

        public EntityActionCaliRange()
        {
            this.Buffer = IsEnable ? "M2.2" : "M2.3";
            this.Name = IsEnable ? "补偿开" : "补偿关";
            Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));// new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));
        }
    }

    //清空当前补偿表
    public class EntityActionClearCali : EntityCmd
    {
        public EntityActionClearCali()
        {
            this.Buffer = "M8";
            this.Name = "清除当前补偿表";
            Icon =new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));
        }
    }

    //激光初始化
    public class EntityActionInitLaser : EntityCmd
    {
        public EntityActionInitLaser()
        {
            this.Buffer = "M3";
            this.Name = "激光初始化";
            Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));
        }
    }

    //延时
    public class EntityActionDwell : EntityCmd
    {
        public int dwellTime = 0;

        [Category("基础"), DisplayName("延时"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public int DwellTime
        {
            get { return dwellTime; }
            set
            {
                dwellTime = value; OnPropertyChanged("DwellTime");
                this.Name = $"DWELL {DwellTime}";
            }
        }

        public EntityActionDwell()
        {
            this.Buffer = $"DWELL {DwellTime}";
            this.Name = $"DWELL {DwellTime}";
            Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));//new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/230.png"));
        }
    }
}
