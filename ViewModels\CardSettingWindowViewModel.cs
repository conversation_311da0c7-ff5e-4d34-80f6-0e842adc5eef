﻿using McLaser.Core.Services.DeviceService.Base.Motion;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace McLaser.EditViewerSk.ViewModels
{
    public class CardSettingWindowViewModel : ObservableObject
    {
        private IMotionCard _card = null;
        public CardSettingWindowViewModel(IMotionCard card)
        {
            _card = card;
            //ViewAxisConfig = new ConfigView(card );
            //ViewCardStatus = new CardStatusView(card);
        }

        public ContentControl ViewAxisConfig { get; set; }
        public ContentControl ViewIOConfig { get; set; }

        public ContentControl ViewCardStatus { get; set; }

    }
}
