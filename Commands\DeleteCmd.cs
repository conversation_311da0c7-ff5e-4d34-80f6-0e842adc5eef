﻿using McLaser.EditViewerSk.Base;
using static McLaser.EditViewerSk.Base.Command;
using System.Collections.Generic;
using System.Windows.Input;
using SkiaSharp;
using System.Windows.Forms;
using MouseEventArgs = System.Windows.Input.MouseEventArgs;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;

namespace McLaser.EditViewerSk.Commands
{

    /// <summary>
    /// 删除命令
    /// </summary>
    //public class DeleteCmd : ModifyCmd
    //{
    //    /// <summary>
    //    /// 操作的图元
    //    /// </summary>
    //    private List<EntityBase> _items = new List<EntityBase>();
    //    private void InitializeItemsToDelete()
    //    {
    //        DocumentBase doc = _mgr.Viewer.Document as DocumentBase;
    //        //foreach (Selection sel in _mgr.presenter.selections)
    //        //{
    //        //    DBObject dbobj = doc.Database.GetObject(sel.objectId);
    //        //    if (dbobj != null && dbobj is Entity)
    //        //    {
    //        //        Entity entity = dbobj as Entity;
    //        //        _items.Add(entity);
    //        //    }
    //        //}
    //    }

    //    public override void Initialize()
    //    {
    //        base.Initialize();

    //        //
    //        //if (_mgr.presenter.selections.Count > 0)
    //        //{
    //        //    InitializeItemsToDelete();
    //        //    _mgr.FinishCurrentCommand();
    //        //}
    //        //else
    //        //{
    //        //    this.pointer.mode = Pointer.Mode.Select;
    //        //}
    //    }

    //    /// <summary>
    //    /// 提交到数据库
    //    /// </summary>
    //    protected override void Commit()
    //    {
    //        foreach (EntityBase item in _items)
    //        {
    //            item.Erase();
    //        }
    //    }

    //    /// <summary>
    //    /// 回滚撤销
    //    /// </summary>
    //    protected override void Rollback()
    //    {
    //        foreach (EntityBase item in _items)
    //        {
    //            _mgr.Viewer.AppendEntity(item);
    //        }
    //    }

    //    public override EventResult OnMouseDown(MouseEventArgs e)
    //    {
    //        return EventResult.Handled;
    //    }

    //    public override EventResult OnMouseUp(MouseEventArgs e)
    //    {
    //        //if (e.Button == MouseButtons.Right)
    //        //{
    //        //    if (_mgr.presenter.selections.Count > 0)
    //        //    {
    //        //        InitializeItemsToDelete();
    //        //        _mgr.FinishCurrentCommand();
    //        //    }
    //        //    else
    //        //    {
    //        //        _mgr.CancelCurrentCommand();
    //        //    }
    //        //}

    //        return EventResult.Handled;
    //    }

    //    public override EventResult OnMouseMove(MouseEventArgs e)
    //    {
    //        return EventResult.Handled;
    //    }

    //    public override EventResult OnKeyDown(KeyEventArgs e)
    //    {
    //        //if (e.KeyCode == Keys.Escape)
    //        //{
    //        //    _mgr.CancelCurrentCommand();
    //        //}

    //        return EventResult.Handled;
    //    }

    //    public override EventResult OnKeyUp(KeyEventArgs e)
    //    {
    //        return EventResult.Handled;
    //    }

    //    public override void OnPaint(ViewBase _viewer)
    //    {
    //    }
    //}
}
