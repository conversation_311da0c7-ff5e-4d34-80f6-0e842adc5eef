﻿
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Interfaces;

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;

using System.Linq;

using Newtonsoft.Json;
using System.Threading;



namespace McLaser.EditViewerSk.Base
{
    public class DocumentBase : INotifyPropertyChanged, IDocument, ICloneable
    {

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public event EventHandler SelectedEntityChanged;

        [JsonIgnore]
        [Browsable(false)]
        public virtual Action Action { get; set; }

        //名称
        public string Name { get; set; }

        //描述
        public string Description { get; set; }

        //版本
        public virtual string Version { get; internal set; }

        //文件名称
        private string fileName = "";
        public virtual string FileName
        {
            get { return fileName; }
            set { fileName = value; OnPropertyChanged("FileName"); }
        }

        //Tag标签
        public object Tag { get; set; }

        //Block列表
        public List<EntityBlock> Blocks { get; }

        //Layer列表
        public ObservableCollection<EntityLayer> Layers { get; set; } = new ObservableCollection<EntityLayer>();

        //笔列表
        public List<EntityPen> Pens { get; set; } = new List<EntityPen>();

        private static readonly object m_lock = new object();
        //已选择实体
        [JsonIgnore]
        private List<EntityBase> _selectedEntitys = new List<EntityBase>();
        public List<EntityBase> SelectedEntitys
        {
            get
            {
                return _selectedEntitys;
            }
            set
            {
                var order = value.OrderBy(x => x.Index).ToList();
                _selectedEntitys = order;
                //OnPropertyChanged(nameof(SelectedEntitys));
                SelectedEntityChanged?.Invoke(_selectedEntitys, new EventArgs());

            }
        }

        //粘贴板实体
        [JsonIgnore]
        public virtual IEnumerable<EntityBase> ClipboardEntitys { get; internal set; }

        //实体字典
        [JsonIgnore]
        public virtual Dictionary<ulong, EntityBase> EntityDic { get; protected set; }

        //笔字典
        [JsonIgnore]
        public virtual Dictionary<int, EntityPen> PenDic { get; protected set; }

        //当前实体
        private EntityLayer _activeLayer;
        [JsonIgnore]
        public virtual EntityLayer ActiveLayer
        {
            get { return _activeLayer; }
            set
            {
                _activeLayer = value;
                OnPropertyChanged(nameof(ActiveLayer));
            }
        }

        //视图
        [JsonIgnore]
        public virtual IView View { get; set; }

        //是否修改文档
        public virtual bool IsModified { get; internal set; }

        //是否Ready
        public virtual bool IsReady { get; set; }

        //文档的外接矩形
        public virtual BoundingBox Dimension { get; set; }

        //自定义的外接矩形
        public BoundingBox[] UserRectangles { get; set; }

        //旋转偏移数据结构
        public virtual Offset RotateOffset { get; set; }

        //编辑器锁定
        public bool IsEditorLock { get; set; }


        public DocumentBase()
        {
            _selections = new List<EntityBase>();

            Version = "0.1";
            this.Name = "NoName{0}";
            this.EntityDic = new Dictionary<ulong, EntityBase>();
            this.PenDic = new Dictionary<int, EntityPen>(16);
            this.ClipboardEntitys = new List<EntityBase>();
            this.IsReady = true;
            Action = new Action(this);
            this.ActiveLayer = null;
            this.Dimension = BoundingBox.Empty;
        }

        public DocumentBase(string name)
         : this()
        {
            this.Name = name;
        }


        public virtual object Clone()
        {
            DocumentBase documentDefault = new()
            {
                Version = Version,
                Name = Name,
                Description = Description,
                FileName = FileName,
                IsEditorLock = IsEditorLock,
                Dimension = (BoundingBox)Dimension.Clone(),
                RotateOffset = RotateOffset.Clone(),
                Tag = Tag,
                Action = Action,
                View = View
            };
            this.Layers.ToList().ForEach(layer => documentDefault.Layers.Add((EntityLayer)layer.Clone()));
            ActiveLayer = this.Layers[0];
            if (UserRectangles != null)
            {
                documentDefault.UserRectangles = new BoundingBox[UserRectangles.Length];
                for (int i = 0; i < UserRectangles.Length; i++)
                {
                    if (UserRectangles[i] != null)
                    {
                        documentDefault.UserRectangles[i] = UserRectangles[i].Clone();
                    }
                }
            }

            return documentDefault;
        }


        //绘制图档
        internal virtual void Render(IView view)
        {
            foreach (EntityLayer entityLayer in Layers)
            {
                entityLayer.Render(view);
                entityLayer.IsNeedToRegen = false;
            }
        }


        public virtual void New()
        {
            Version = Config.DocumentVersion;
            Name = string.Empty;
            Description = string.Empty;
            Layers.Clear();
            EntityLayer layer = new EntityLayer($"图层{Action.NewLayerIndex++}");
            Layers.Add(layer);
            ActiveLayer = layer;
            Blocks.Clear();
            IsEditorLock = false;
            ((ViewBase)View).OnZoomFit();
            Action.UndoRedoClear();
            Action.SelectedEntity = null;
        }



        /// <summary>
        /// 选择集
        /// </summary>
        private List<EntityBase> _selections = null;
        [JsonIgnore]
        public List<EntityBase> Selections
        {
            get { return _selections; }
            set
            {
                _selections = value;

            }
        }



    }
}
