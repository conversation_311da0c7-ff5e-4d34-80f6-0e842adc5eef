﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

namespace McLaser.EditViewerSk.UndoRedo
{
 


    internal class UndoRedoEntityReverse : UndoRedoSingle
    {
        private DocumentBase doc;
        private EntityLayer layer;
        private List<EntityBase> list;
        private UndoRedoMultiple urs;

        public override void Execute()
        {
            this.urs = new UndoRedoMultiple();
            int index = this.list.Last<EntityBase>().Index;
            List<EntityBase> entityList;
            if (list.Count == 1 && list[0] is EntityGroup)
               entityList = new List<EntityBase>((this.list[0] as EntityGroup).Entities);
            else entityList = new List<EntityBase>((IEnumerable<EntityBase>)this.list);
           

            var sortedSegments = new List<EntityBase>();
            var remainingSegments = new HashSet<dynamic>(entityList);
            try
            {

                foreach (var segment in remainingSegments)
                {
                    var tmp = ReverseDirection(segment);
                    sortedSegments.Add(tmp);
                }
            }
            catch (Exception ex)
            {

            }

            if (list.Count == 1 && list[0] is EntityGroup)
            {
                var group = ((EntityGroup)(list[0] as EntityGroup).Clone());
                group.Entities = sortedSegments;
                sortedSegments = new List<EntityBase>() { group };
            }

                this.urs = new UndoRedoMultiple();
            index = doc.ActiveLayer.Children.IndexOf(list.First());
            this.urs.Add((IUndoRedo)new UndoRedoEntityDelete(this.doc, this.list));
            this.urs.Add((IUndoRedo)new UndoRedoEntityAdd(this.doc, this.layer, sortedSegments, index));
            this.urs.Execute();
        }


        public dynamic ReverseDirection(dynamic segment)
        {
            if (segment is EntityLine)
            {
                var temp = segment.StartPoint;
                segment.StartPoint = segment.EndPoint;
                segment.EndPoint = temp;
                return segment;
            }
            else if (segment is EntityArc)
            {
                var temp = segment.StartAngle;
                segment.StartAngle = temp + segment.SweepAngle;
                //segment.EndAngle = temp - segment.SweepAngle;
                segment.SweepAngle = -segment.SweepAngle;
                return segment;
            }
            return segment;

        }
        public override void Undo() => this.urs.Undo();

        public override void Redo() => this.urs.Redo();

        public UndoRedoEntityReverse(DocumentBase doc, EntityLayer layer,List<EntityBase> list)
        {
            this.Name = "Entity Reverse";
            this.doc = doc;
            this.layer = layer;
            this.list = new List<EntityBase>((IEnumerable<EntityBase>)list);
            
        }
    }
}
