﻿using SkiaSharp;
using SkiaSharp.Views.Desktop;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Views
{
    public partial class GLEditor : UserControl
    {
        private SKGLControl _skglControl;
        public GLEditor()
        {
            InitializeComponent();
            _skglControl = new SKGLControl();
            _skglControl.Dock = System.Windows.Forms.DockStyle.Fill;
            _skglControl.PaintSurface += OnPaintSurface;
            this.Controls.Add(_skglControl);    
        }


        private void OnPaintSurface(object sender, SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs e)
        {
            var canvas = e.Surface.Canvas;
            canvas.Clear(SKColors.White);

            // 示例绘图
            using (var paint = new SKPaint
            {
                Color = SKColors.Red,
                IsAntialias = true,
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 5
            })
            {
                canvas.DrawCircle(100, 100, 50, paint);
            }
        }
    }
}
