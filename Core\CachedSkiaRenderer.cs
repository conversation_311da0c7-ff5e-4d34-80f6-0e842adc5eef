
using SkiaSharp;
using System.Collections.Generic;
using McLaser.EditViewerSk.Entitys;
using System.Linq;
using McLaser.EditViewerSk.Spatial;

namespace McLaser.EditViewerSk.Core
{
    public class CachedSkiaRenderer : I<PERSON>enderer
    {
        private readonly Quadtree _quadtree;
        private readonly Dictionary<EntityBase, SKPath> _pathCache = new Dictionary<EntityBase, SKPath>();
        private readonly SKPaint _defaultPaint = new SKPaint { Style = SKPaintStyle.Stroke, IsAntialias = true, StrokeWidth = 10, Color = SKColors.Black };
        private readonly SKPaint _selectedPaint = new SKPaint { Style = SKPaintStyle.Stroke, IsAntialias = true, StrokeWidth = 15, Color = SKColors.Red };

        public CachedSkiaRenderer(SKRect worldBounds)
        {
            _quadtree = new Quadtree(0, worldBounds);
        }

        public void AddEntity(EntityBase entity)
        {
            if (entity == null || _pathCache.ContainsKey(entity)) return;

            var path = CreatePathForEntity(entity);
            if (path != null)
            {
                _pathCache[entity] = path;
                _quadtree.Insert(entity);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"警告: 无法为图元创建路径: {entity?.GetType().Name}");
            }
        }

        public void RemoveEntity(EntityBase entity)
        {
            if (entity != null && _pathCache.TryGetValue(entity, out var path))
            {
                _pathCache.Remove(entity);
                path.Dispose();
                RebuildQuadtree();
            }
        }

        public void UpdateEntity(EntityBase entity)
        {
            if (entity == null) return;
            if (_pathCache.TryGetValue(entity, out var oldPath))
            {
                oldPath.Dispose();
            }

            var newPath = CreatePathForEntity(entity);
            if (newPath != null)
            {
                _pathCache[entity] = newPath;
                RebuildQuadtree();
            }
        }

        public void Clear()
        {
            foreach (var path in _pathCache.Values) path.Dispose();
            _pathCache.Clear();
            _quadtree.Clear();
        }

        private void RebuildQuadtree()
        {
            _quadtree.Clear();
            foreach (var entity in _pathCache.Keys)
            {
                _quadtree.Insert(entity);
            }
        }

        public void Render(SKCanvas canvas, SKRect visibleWorldRect)
        {
            var entitiesToRender = _quadtree.Query(visibleWorldRect);
            int renderedCount = 0;

            System.Diagnostics.Debug.WriteLine($"渲染器: 缓存中有 {_pathCache.Count} 个图元，查询到 {entitiesToRender.Count} 个可见图元");

            // 移除测试矩形，问题已解决

            int checkedCount = 0;
            int failedVisibilityCount = 0;
            int failedPathCount = 0;

            foreach (IQuadtreeObject item in entitiesToRender)
            {
                checkedCount++;
                var entity = item as EntityBase;

                if (entity == null)
                {
                    if (checkedCount <= 5) System.Diagnostics.Debug.WriteLine($"图元 {checkedCount}: entity为null");
                    continue;
                }

                if (!entity.IsRenderable)
                {
                    if (checkedCount <= 5) System.Diagnostics.Debug.WriteLine($"图元 {checkedCount}: {entity.GetType().Name} 不可渲染");
                    failedVisibilityCount++;
                    continue;
                }

                if (!entity.IsVisible)
                {
                    if (checkedCount <= 5) System.Diagnostics.Debug.WriteLine($"图元 {checkedCount}: {entity.GetType().Name} 不可见");
                    failedVisibilityCount++;
                    continue;
                }

                if (!_pathCache.TryGetValue(entity, out var path))
                {
                    if (checkedCount <= 5) System.Diagnostics.Debug.WriteLine($"图元 {checkedCount}: {entity.GetType().Name} 路径缓存中不存在");
                    failedPathCount++;
                    continue;
                }

                var paint = entity.IsSelected ? _selectedPaint : _defaultPaint;
                // 修复：确保使用黑色作为默认颜色，而不是图元的颜色（可能是白色）
                paint.Color = entity.IsSelected ? SKColors.Red : SKColors.Black;

                // 简化调试输出
                if (renderedCount < 3)
                {
                    System.Diagnostics.Debug.WriteLine($"渲染图元 {renderedCount + 1}: {entity.GetType().Name}, 颜色: {paint.Color}");
                }

                canvas.DrawPath(path, paint);
                renderedCount++;
            }

            System.Diagnostics.Debug.WriteLine($"渲染统计: 检查了{checkedCount}个图元，可见性失败{failedVisibilityCount}个，路径失败{failedPathCount}个，成功渲染{renderedCount}个");

            System.Diagnostics.Debug.WriteLine($"实际渲染了 {renderedCount} 个图元");
        }

        public IEnumerable<EntityBase> Query(SKRect queryRect)
        {
            return _quadtree.Query(queryRect).Cast<EntityBase>();
        }

        private SKPath CreatePathForEntity(EntityBase entity)
        {
            var path = new SKPath();
            switch (entity)
            {
                case EntityLine line:
                    path.MoveTo((float)line.StartPoint.X, (float)line.StartPoint.Y);
                    path.LineTo((float)line.EndPoint.X, (float)line.EndPoint.Y);
                    // 调试第一个线段
                    if (_pathCache.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"创建第一个线段路径: ({line.StartPoint.X}, {line.StartPoint.Y}) -> ({line.EndPoint.X}, {line.EndPoint.Y})");
                        System.Diagnostics.Debug.WriteLine($"路径边界: {path.Bounds}");
                    }
                    break;
                case EntityCircle circle:
                    path.AddCircle((float)circle.Center.X, (float)circle.Center.Y, (float)circle.Radius);
                    break;
                case EntityArc arc:
                    var rect = SKRect.Create((float)(arc.Center.X - arc.Radius), (float)(arc.Center.Y - arc.Radius), (float)(arc.Radius * 2), (float)(arc.Radius * 2));
                    path.AddArc(rect, (float)arc.StartAngle, (float)arc.SweepAngle);
                    break;
                case EntityPolyline2D polyline2d:
                    // EntityPolyline2D包含子图元（线段和弧段），需要递归处理
                    if (polyline2d.Entities != null && polyline2d.Entities.Count > 0)
                    {
                        foreach (var subEntity in polyline2d.Entities)
                        {
                            var subPath = CreatePathForEntity(subEntity);
                            if (subPath != null)
                            {
                                path.AddPath(subPath);
                                subPath.Dispose();
                            }
                        }
                    }
                    else if (polyline2d.Points.Count > 1)
                    {
                        // 备用方案：直接使用点列表
                        path.MoveTo((float)polyline2d.Points[0].X, (float)polyline2d.Points[0].Y);
                        for (int i = 1; i < polyline2d.Points.Count; i++)
                        {
                            path.LineTo((float)polyline2d.Points[i].X, (float)polyline2d.Points[i].Y);
                        }
                        if (polyline2d.IsClosed)
                        {
                            path.Close();
                        }
                    }
                    break;
                case EntityLwPolyline polyline:
                    if (polyline.Vertexes.Count > 1)
                    {
                        path.MoveTo((float)polyline.Vertexes[0].X, (float)polyline.Vertexes[0].Y);
                        for (int i = 1; i < polyline.Vertexes.Count; i++)
                        {
                            path.LineTo((float)polyline.Vertexes[i].X, (float)polyline.Vertexes[i].Y);
                        }
                        if (polyline.IsClosed)
                        {
                            path.Close();
                        }
                    }
                    break;
                default:
                    path.Dispose();
                    return null;
            }
            return path;
        }
    }
}
