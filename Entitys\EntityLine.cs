﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Marker;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Media.Imaging;
using static System.Windows.Forms.AxHost;

namespace McLaser.EditViewerSk.Entitys
{
    public class EntityLine : EntityBase
    {
        private double angle;
        private double startRampFactor;
        private double endRampFactor;
        private Vector2 _startPoint = new Vector2();
        private Vector2 _endPoint = new Vector2();

        [Category("基础"), DisplayName("起点"),Browsable(true), RefreshProperties(RefreshProperties.All)]
        public Vector2 StartPoint
        {
            get { return _startPoint; }
            set { _startPoint = value; this.IsNeedToRegen = true; }
        }


        [Category("基础"), DisplayName("终点"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public Vector2 EndPoint
        {
            get { return _endPoint; }
            set { _endPoint = value; this.IsNeedToRegen = true; }
        }


        [Category("基础"), DisplayName("设定角度")]
        public override double Angle
        {
            get => this.angle;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                double angle = value - this.angle;
                if (this.Parent != null)
                    this.Rotate(angle);
                this.angle = value;
            }
        }


        [Category("基础"), DisplayName("长度"), Browsable(true), RefreshProperties(RefreshProperties.All)]
        public double Length
        {
            get
            {
                return Vector2.Distance(this.StartPoint, this.EndPoint);
            }
            set
            {
                if (value <= 0.0)
                {
                    return;
                }
                if (!IsLocked)
                {
                    Vector2 vec = this.EndPoint - this.StartPoint;
                    vec.Normalize();
                    this.EndPoint = this.StartPoint + (float)value * vec;
                }
            }
        }


        [Category("基础"), DisplayName("角度"), ReadOnly(true), JsonIgnore]
        public double LineVectorAngle
        {
            get
            {
                Vector2 vector2 = this.EndPoint - this.StartPoint;
                return MathHelper.NormalizeAngle(Math.Atan2((double)vector2.Y, (double)vector2.X) * 57.29578f);
            }
        }

        public EntityLine()
        {
            Name = "line";
            Icon =  new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/line.png"));
        }

        public EntityLine(double x1, double y1, double x2, double y2) : this()
        {
            this.StartPoint = new Vector2(x1, y1);
            this.EndPoint = new Vector2(x2, y2);
            Icon =  new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/line.png"));
        }

        public EntityLine(double x1, double y1, double x2, double y2, double rampStart = 1, double rampEnd = 1)
         : this(x1, y1, x2, y2)
        {
            this.startRampFactor = rampStart;
            this.endRampFactor = rampEnd;
        }
        


        public override void Render(IView view)
        {
            if (view == null || !this.IsRenderable) return;
            if (this.IsNeedToRegen) this.Regen();

            Pen.Color = IsSelected ? SKColors.Red : SKColors.Black;
            (view as ViewBase).DrawLine(_startPoint, _endPoint, Pen);
            if (IsSelected) (view as ViewBase).DrawArrow(_startPoint, _endPoint, 10);
        }

        public override bool Mark(IMarkerArg markerArg, IMarkCommand cmd)
        {
            if (!this.IsMarkerable)
                return true;
            bool flag = true;

            if (!this.IsReversable)
            {
                flag &= cmd.MarkLinear(markerArg, this, out string buffer);
                if (!flag)
                    return false;
            }

            return flag;
        }

        private void RegenVertextList()
        {
        }

        [JsonIgnore]
        public override BoundingBox BoundingBox { get; set; } = new BoundingBox();

        private void RegenBoundRect()
        {
            double minX = Math.Min(StartPoint.X, EndPoint.X);
            double minY = Math.Min(StartPoint.Y, EndPoint.Y);
            double maxX = Math.Max(StartPoint.X, EndPoint.X);
            double maxY = Math.Max(StartPoint.Y, EndPoint.Y);

            this.BoundingBox.Set(minX, minY, maxX, maxY);
        }

        public override void Regen()
        {
            this.RegenVertextList();
            this.RegenBoundRect();
            this.IsNeedToRegen = false;
        }



        public override void Translate(Vector2 delta)
        {
            if (this.IsLocked || delta == Vector2.Zero)
                return;
            this.StartPoint = Vector2.Add(this.StartPoint, delta);
            this.EndPoint = Vector2.Add(this.EndPoint, delta);
            this.BoundingBox.Transit(delta);
        }

        public override void Rotate(double angle)
        {
            if (this.IsLocked || MathHelper.IsZero(angle))
                return;
            this.StartPoint = Vector2.Transform(StartPoint, Matrix3.CreateRotation(angle * ((float)Math.PI / 180f), this.BoundingBox.Center));
            this.EndPoint = Vector2.Transform(this.EndPoint, Matrix3.CreateRotation(angle * ((float)Math.PI / 180f), this.BoundingBox.Center));
            this.angle += angle;
            this.angle = MathHelper.NormalizeAngle(this.angle);
            this.Regen();
        }

        public override void Rotate(double angle, Vector2 rotateCenter)
        {
            if (this.IsLocked || MathHelper.IsZero(angle))
                return;
            this.StartPoint = Vector2.Transform(this.StartPoint, Matrix3.CreateRotation(angle * ((float)Math.PI / 180f), rotateCenter));
            this.EndPoint = Vector2.Transform(this.EndPoint, Matrix3.CreateRotation(angle * ((float)Math.PI / 180f), rotateCenter));
            this.angle += angle;
            this.angle = MathHelper.NormalizeAngle(this.angle);
            this.Regen();
        }

        public override void Scale(Vector2 scale)
        {
            if (this.IsLocked || scale == Vector2.Zero || scale == Vector2.One)
                return;
            Vector2 vector2 = (this.StartPoint + this.EndPoint) * 0.5f;
            this.StartPoint = (this.StartPoint - vector2) * scale + vector2;
            this.EndPoint = (this.EndPoint - vector2) * scale + vector2;
            this.Regen();
        }

        public override void Scale(Vector2 scale, Vector2 scaleCenter)
        {
            if (this.IsLocked || scale == Vector2.Zero || scale == Vector2.One)
                return;
            this.StartPoint = (this.StartPoint - scaleCenter) * scale + scaleCenter;
            this.EndPoint = (this.EndPoint - scaleCenter) * scale + scaleCenter;
            this.Regen();
        }


        public override bool HitTest(BoundingBox br, double threshold)
        {
            return this.BoundingBox.HitTest(br, threshold) &&
                MathHelper.IntersectLineInRect(br, StartPoint.X, StartPoint.Y, EndPoint.X, EndPoint.Y);
        }

        public override bool HitTest(double left, double top, double right, double bottom, double threshold)
        {
            return this.HitTest(new BoundingBox(left, top, right, bottom), threshold);
        }

        public override bool HitTest(double x, double y, double threshold)
        {
            return this.BoundingBox.HitTest(x, y, threshold)
                && MathHelper.IntersectPointInLine(StartPoint.X, StartPoint.Y, EndPoint.X, EndPoint.Y, x, y, threshold);
        }

        /// <summary>
        /// 对象捕捉点
        /// </summary>
        public override List<ObjectSnapPoint> GetSnapPoints()
        {
            List<ObjectSnapPoint> snapPnts = new List<ObjectSnapPoint>
            {
                new ObjectSnapPoint(ObjectSnapMode.End, _startPoint),
                new ObjectSnapPoint(ObjectSnapMode.End, _endPoint),
                new ObjectSnapPoint(ObjectSnapMode.Mid, (_startPoint + _endPoint) / 2)
            };

            return snapPnts;
        }

        public override object Clone()
        {
            return new EntityLine()
            {
                Name = this.Name,
                Description = this.Description,
                Parent = this.Parent,
                IsSelected = this.IsSelected,
                IsRenderable = this.IsRenderable,
                IsLocked = this.IsLocked,
                StartPoint = this.StartPoint,
              
                EndPoint = this.EndPoint,
            
                BoundingBox = this.BoundingBox.Clone(),
                Color = this.Color, 
                Tag = this.Tag,
                Index = this.Index,
                IsNeedToRegen = true
            };
        }

    }
}
