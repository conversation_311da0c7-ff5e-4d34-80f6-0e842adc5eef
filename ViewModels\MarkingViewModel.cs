﻿using McLaser.Core.Services.DeviceService.Base.Motion;
using McLaser.EditViewerSk.Marker;
using McLaser.EditViewerSk.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.ViewModels
{
    public class MarkingViewModel : ObservableObject
    {
        protected IMotionCard Card { get; set; }
        protected IMarkerArg MarkerArg { get; set; }    
        protected IMarker Marker { get; set; }

        public RelayCommand MarkFullCommand => new RelayCommand(MarkFull);
        public RelayCommand MarkSelectedCommand => new RelayCommand(MarkSelected);
        public RelayCommand MarkPauseCommand => new RelayCommand(MarkPause);
        public RelayCommand MarkStopCommand => new RelayCommand(MarkStop);
        public RelayCommand OpenMarkerSettingCommand => new RelayCommand(OpenMarkerSetting);
        public RelayCommand OpenCardSettingCommand => new RelayCommand(OpenCardSetting);

        public bool _simulation = false;
        public bool IsSimulation
        {
            get => _simulation;
            set => SetProperty(ref _simulation, value);
        }

        public MarkingViewModel(IMarker marker)
        {
            IsSimulation = false;
            Marker = marker;
            Card = marker.Card; 
            MarkerArg = marker.MarkerArg;
        }

        //全图加工
        private void MarkFull()
        {
            if(Marker == null) return;

            Marker.Ready(Marker.MarkerArg);
            Marker.Start(0);
        }

        //选中加工
        private void MarkSelected()
        {
        }

        //暂停加工
        private void MarkPause()
        {
        }

        //停止加工
        private void MarkStop()
        {
            if (Marker == null) return;

            Marker.Stop();
        }

        //打开加工参数
        private void OpenMarkerSetting()
        {
            SettingWindow markerSettingWindow = new SettingWindow();
            markerSettingWindow.ShowDialog();
        }

        //打开控制卡参数
        private void OpenCardSetting()
        {
            CardSettingWindow cardSettingWindow = new CardSettingWindow(Card as  IMotionCard);
            cardSettingWindow.ShowDialog();
        }
    }
}
